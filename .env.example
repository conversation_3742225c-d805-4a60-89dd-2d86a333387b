# Database
DATABASE_URL="postgresql://ottiq_user:ottiq_password@localhost:5432/ottiq_dev"

# NextAuth.js
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here"

# OAuth Providers (required for AI try-on)
GOOGLE_CLIENT_ID=""
GOOGLE_CLIENT_SECRET=""
FACEBOOK_CLIENT_ID=""
FACEBOOK_CLIENT_SECRET=""

# Admin Configuration
ADMIN_EMAILS="<EMAIL>,<EMAIL>"

# MinIO/S3 Configuration
MINIO_ENDPOINT="localhost:9000"
MINIO_ACCESS_KEY="ottiq_minio"
MINIO_SECRET_KEY="ottiq_minio_password"
MINIO_BUCKET_NAME="ottiq-uploads"
MINIO_USE_SSL="false"

# Redis
REDIS_URL="redis://localhost:6379"

# Email Configuration (using Mailhog for development)
SMTP_HOST="localhost"
SMTP_PORT="1025"
SMTP_USER=""
SMTP_PASSWORD=""
SMTP_FROM="<EMAIL>"

# AI Services
HUGGING_FACE_API_KEY=""
OPENAI_API_KEY=""

# AI Try-On Configuration
# Hugging Face OOTDiffusion Space URL
HUGGINGFACE_OOTD_SPACE_URL="https://huggingface.co/spaces/levihsu/OOTDiffusion"

# AI Try-On Settings
AI_IMAGE_TARGET_SIZE="512"
AI_TRYON_RATE_LIMIT="10" # Max requests per user per hour
MAX_CONCURRENT_AI_JOBS="5"
AI_TRYON_TIMEOUT="120000" # 2 minutes in milliseconds

# Image Security
ENABLE_IMAGE_SECURITY_SCAN="false"
IMAGE_SECURITY_SERVICE_URL=""
IMAGE_SECURITY_API_KEY=""

# Application Settings
NODE_ENV="development"
APP_URL="http://localhost:3000"

# File Upload Settings
MAX_FILE_SIZE="10485760" # 10MB in bytes
ALLOWED_FILE_TYPES="image/jpeg,image/png,image/webp"

# Rate Limiting
RATE_LIMIT_REQUESTS="100"
RATE_LIMIT_WINDOW="900000" # 15 minutes in milliseconds

# bKash Payment Gateway Configuration
# Sandbox Configuration (for development)
BKASH_SANDBOX="true"
BKASH_BASE_URL="https://tokenized.sandbox.bka.sh/v1.2.0-beta/tokenized"
BKASH_VERSION="v1.2.0-beta"
BKASH_APP_KEY="your_sandbox_app_key"
BKASH_APP_SECRET="your_sandbox_app_secret"
BKASH_USERNAME="01XXXXXXXXX"
BKASH_PASSWORD="your_sandbox_password"

# Production Configuration (uncomment for live)
# BKASH_SANDBOX="false"
# BKASH_BASE_URL="https://tokenized.pay.bka.sh/v1.2.0-beta/tokenized"
# BKASH_VERSION="v1.2.0-beta"
# BKASH_APP_KEY="your_live_app_key"
# BKASH_APP_SECRET="your_live_app_secret"
# BKASH_USERNAME="01XXXXXXXXX"
# BKASH_PASSWORD="your_live_password"

# Payment Configuration
PAYMENT_CURRENCY="BDT"
PAYMENT_TIMEOUT="30000" # 30 seconds in milliseconds
PAYMENT_CALLBACK_URL="${APP_URL}/api/payments/bkash/callback"
PAYMENT_SUCCESS_URL="${APP_URL}/checkout/success"
PAYMENT_FAILURE_URL="${APP_URL}/checkout/failure"
PAYMENT_CANCEL_URL="${APP_URL}/checkout/cancel"

# Shipping Configuration (Bangladesh)
SHIPPING_ENABLED="true"
SHIPPING_BASE_COST="50" # Base shipping cost in BDT
SHIPPING_FREE_THRESHOLD="1000" # Free shipping above this amount
SHIPPING_ZONES="dhaka,chittagong,sylhet,rajshahi,khulna,barisal,rangpur,mymensingh"
SHIPPING_DELIVERY_DAYS="3-7" # Estimated delivery days
