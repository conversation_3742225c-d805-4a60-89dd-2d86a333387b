import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { PrismaClient } from '@prisma/client';
import { getBkashService } from '@/lib/services/bkash';

const prisma = new PrismaClient();

/**
 * GET /api/payments/[id] - Get payment details
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          error: 'Authentication required',
        },
        { status: 401 }
      );
    }

    const payment = await prisma.payment.findFirst({
      where: {
        id: params.id,
        userId: session.user.id,
      },
      include: {
        order: {
          select: {
            id: true,
            orderNumber: true,
            status: true,
            totalAmount: true,
            currency: true,
            createdAt: true,
          },
        },
      },
    });

    if (!payment) {
      return NextResponse.json(
        {
          success: false,
          error: 'Payment not found',
        },
        { status: 404 }
      );
    }

    // Remove sensitive data from response
    const safePayment = {
      id: payment.id,
      amount: payment.amount,
      currency: payment.currency,
      status: payment.status,
      method: payment.method,
      bkashTransactionId: payment.bkashTransactionId,
      refundAmount: payment.refundAmount,
      refundedAt: payment.refundedAt,
      createdAt: payment.createdAt,
      updatedAt: payment.updatedAt,
      order: payment.order,
    };

    return NextResponse.json({
      success: true,
      data: safePayment,
    });
  } catch (error) {
    console.error('Get payment error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to retrieve payment',
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/payments/[id]/verify - Verify payment status with gateway
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          error: 'Authentication required',
        },
        { status: 401 }
      );
    }

    const payment = await prisma.payment.findFirst({
      where: {
        id: params.id,
        userId: session.user.id,
      },
    });

    if (!payment) {
      return NextResponse.json(
        {
          success: false,
          error: 'Payment not found',
        },
        { status: 404 }
      );
    }

    // Verify payment based on method
    if (payment.method === 'bkash' && payment.bkashPaymentId) {
      const bkashService = getBkashService();
      
      try {
        const queryResult = await bkashService.queryPayment(payment.bkashPaymentId);
        
        // Update payment status based on query result
        const isSuccessful = bkashService.isPaymentSuccessful(queryResult.transactionStatus);
        const isFailed = bkashService.isPaymentFailed(queryResult.transactionStatus);
        
        let newStatus = payment.status;
        let newOrderStatus = null;
        
        if (isSuccessful && payment.status !== 'COMPLETED') {
          newStatus = 'COMPLETED';
          newOrderStatus = 'CONFIRMED';
        } else if (isFailed && !['COMPLETED', 'REFUNDED'].includes(payment.status)) {
          newStatus = 'FAILED';
          newOrderStatus = 'PAYMENT_FAILED';
        }
        
        // Update payment if status changed
        if (newStatus !== payment.status) {
          await prisma.payment.update({
            where: { id: payment.id },
            data: {
              status: newStatus,
              bkashTransactionId: queryResult.trxID || payment.bkashTransactionId,
              gatewayResponse: queryResult,
            },
          });
          
          // Update order status if needed
          if (newOrderStatus) {
            await prisma.order.update({
              where: { id: payment.orderId },
              data: {
                status: newOrderStatus,
                paymentStatus: newStatus,
              },
            });
          }
        }
        
        return NextResponse.json({
          success: true,
          data: {
            paymentId: payment.id,
            status: newStatus,
            gatewayStatus: queryResult.transactionStatus,
            transactionId: queryResult.trxID,
            amount: queryResult.amount,
            currency: queryResult.currency,
            message: bkashService.getPaymentStatusMessage(queryResult.transactionStatus),
            lastVerified: new Date().toISOString(),
          },
        });
      } catch (error) {
        console.error('bKash payment verification error:', error);
        return NextResponse.json(
          {
            success: false,
            error: 'Payment verification failed',
            message: 'Unable to verify payment status with bKash',
          },
          { status: 500 }
        );
      }
    } else if (payment.method === 'cash_on_delivery') {
      // COD payments don't need gateway verification
      return NextResponse.json({
        success: true,
        data: {
          paymentId: payment.id,
          status: payment.status,
          message: 'Cash on delivery payment - no gateway verification needed',
          lastVerified: new Date().toISOString(),
        },
      });
    } else {
      return NextResponse.json(
        {
          success: false,
          error: 'Unsupported payment method for verification',
        },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Payment verification error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Payment verification failed',
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/payments/[id] - Update payment (admin only)
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          error: 'Authentication required',
        },
        { status: 401 }
      );
    }

    // Check if user is admin
    const adminEmails = process.env.ADMIN_EMAILS?.split(',') || [];
    if (!adminEmails.includes(session.user.email || '')) {
      return NextResponse.json(
        {
          success: false,
          error: 'Admin access required',
        },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { status, refundAmount, refundReason, adminNotes } = body;

    const payment = await prisma.payment.findUnique({
      where: { id: params.id },
    });

    if (!payment) {
      return NextResponse.json(
        {
          success: false,
          error: 'Payment not found',
        },
        { status: 404 }
      );
    }

    // Update payment
    const updatedPayment = await prisma.payment.update({
      where: { id: params.id },
      data: {
        ...(status && { status }),
        ...(refundAmount && { 
          refundAmount: parseFloat(refundAmount),
          refundedAt: new Date(),
          refundReason,
        }),
        gatewayResponse: {
          ...payment.gatewayResponse,
          adminUpdate: {
            updatedBy: session.user.id,
            updatedAt: new Date().toISOString(),
            notes: adminNotes,
          },
        },
      },
    });

    // Update order status if payment status changed
    if (status) {
      let orderStatus = null;
      let paymentStatus = status;

      if (status === 'COMPLETED') {
        orderStatus = 'CONFIRMED';
      } else if (status === 'FAILED') {
        orderStatus = 'PAYMENT_FAILED';
      } else if (status === 'REFUNDED') {
        orderStatus = 'REFUNDED';
      }

      if (orderStatus) {
        await prisma.order.update({
          where: { id: payment.orderId },
          data: {
            status: orderStatus,
            paymentStatus,
          },
        });
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        id: updatedPayment.id,
        status: updatedPayment.status,
        refundAmount: updatedPayment.refundAmount,
        refundedAt: updatedPayment.refundedAt,
        updatedAt: updatedPayment.updatedAt,
      },
      message: 'Payment updated successfully',
    });
  } catch (error) {
    console.error('Payment update error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update payment',
      },
      { status: 500 }
    );
  }
}
