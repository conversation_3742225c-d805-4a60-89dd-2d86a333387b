import { NextRequest, NextResponse } from 'next/server';
import { PaymentService } from '@/lib/services/payment';
import { getBkashService } from '@/lib/services/bkash';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

/**
 * GET /api/payments/bkash/callback - Handle bKash payment callback
 * 
 * This endpoint is called by bKash after user completes payment authorization.
 * It executes the payment and redirects user to appropriate success/failure page.
 */
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const paymentID = searchParams.get('paymentID');
  const status = searchParams.get('status');

  console.log('bKash callback received:', { paymentID, status, searchParams: Object.fromEntries(searchParams) });

  try {
    if (!paymentID) {
      console.error('bKash callback: Missing paymentID');
      return NextResponse.redirect(
        new URL(`/checkout/failure?error=missing_payment_id`, request.url)
      );
    }

    // Find the payment record
    const payment = await prisma.payment.findFirst({
      where: {
        bkashPaymentId: paymentID,
      },
      include: {
        order: {
          include: {
            user: {
              select: {
                id: true,
                email: true,
                name: true,
              },
            },
          },
        },
      },
    });

    if (!payment) {
      console.error('bKash callback: Payment not found for paymentID:', paymentID);
      return NextResponse.redirect(
        new URL(`/checkout/failure?error=payment_not_found`, request.url)
      );
    }

    // Check if payment was cancelled by user
    if (status === 'cancel' || status === 'failure') {
      console.log('bKash callback: Payment cancelled or failed by user');
      
      // Update payment status
      await prisma.payment.update({
        where: { id: payment.id },
        data: {
          status: 'CANCELLED',
          failureReason: `User ${status === 'cancel' ? 'cancelled' : 'failed'} payment`,
        },
      });

      // Update order status
      await prisma.order.update({
        where: { id: payment.orderId },
        data: {
          status: 'PAYMENT_FAILED',
          paymentStatus: 'CANCELLED',
        },
      });

      return NextResponse.redirect(
        new URL(`/checkout/cancelled?orderId=${payment.orderId}`, request.url)
      );
    }

    // Execute the payment
    const paymentService = new PaymentService();
    const result = await paymentService.executeBkashPayment(paymentID);

    if (result.success) {
      console.log('bKash payment executed successfully:', {
        paymentId: payment.id,
        orderId: payment.orderId,
        transactionId: result.data?.transactionId,
      });

      // Log successful payment for analytics
      console.log('Payment completed:', {
        orderId: payment.orderId,
        userId: payment.userId,
        amount: payment.amount,
        method: 'bkash',
        transactionId: result.data?.transactionId,
        timestamp: new Date().toISOString(),
      });

      return NextResponse.redirect(
        new URL(`/checkout/success?orderId=${payment.orderId}&transactionId=${result.data?.transactionId}`, request.url)
      );
    } else {
      console.error('bKash payment execution failed:', result.error);

      return NextResponse.redirect(
        new URL(`/checkout/failure?orderId=${payment.orderId}&error=${result.error?.code}`, request.url)
      );
    }
  } catch (error) {
    console.error('bKash callback error:', error);

    // Try to find payment for error logging
    if (paymentID) {
      try {
        await prisma.payment.updateMany({
          where: {
            bkashPaymentId: paymentID,
          },
          data: {
            status: 'FAILED',
            failureReason: `Callback processing error: ${error instanceof Error ? error.message : 'Unknown error'}`,
          },
        });
      } catch (updateError) {
        console.error('Failed to update payment status after callback error:', updateError);
      }
    }

    return NextResponse.redirect(
      new URL(`/checkout/failure?error=callback_processing_failed`, request.url)
    );
  }
}

/**
 * POST /api/payments/bkash/callback - Handle bKash webhook (if supported)
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    const signature = request.headers.get('x-bkash-signature');

    console.log('bKash webhook received:', {
      hasSignature: !!signature,
      bodyLength: body.length,
    });

    // Verify webhook signature if provided
    if (signature) {
      const bkashService = getBkashService();
      const webhookSecret = process.env.BKASH_WEBHOOK_SECRET || '';
      
      if (!bkashService.verifyWebhookSignature(body, signature, webhookSecret)) {
        console.error('bKash webhook: Invalid signature');
        return NextResponse.json(
          { success: false, error: 'Invalid signature' },
          { status: 401 }
        );
      }
    }

    // Parse webhook payload
    const webhookData = JSON.parse(body);
    console.log('bKash webhook data:', webhookData);

    // Process webhook based on event type
    if (webhookData.eventType === 'payment.completed') {
      await this.handlePaymentCompletedWebhook(webhookData);
    } else if (webhookData.eventType === 'payment.failed') {
      await this.handlePaymentFailedWebhook(webhookData);
    } else if (webhookData.eventType === 'payment.refunded') {
      await this.handlePaymentRefundedWebhook(webhookData);
    }

    return NextResponse.json({ success: true, message: 'Webhook processed' });
  } catch (error) {
    console.error('bKash webhook processing error:', error);
    return NextResponse.json(
      { success: false, error: 'Webhook processing failed' },
      { status: 500 }
    );
  }
}

/**
 * Handle payment completed webhook
 */
async function handlePaymentCompletedWebhook(webhookData: any) {
  try {
    const { paymentID, trxID, amount } = webhookData.data;

    const payment = await prisma.payment.findFirst({
      where: {
        bkashPaymentId: paymentID,
      },
    });

    if (payment && payment.status !== 'COMPLETED') {
      await prisma.payment.update({
        where: { id: payment.id },
        data: {
          status: 'COMPLETED',
          bkashTransactionId: trxID,
          gatewayResponse: webhookData,
        },
      });

      await prisma.order.update({
        where: { id: payment.orderId },
        data: {
          status: 'CONFIRMED',
          paymentStatus: 'COMPLETED',
        },
      });

      console.log('Payment completed via webhook:', { paymentID, trxID });
    }
  } catch (error) {
    console.error('Error handling payment completed webhook:', error);
  }
}

/**
 * Handle payment failed webhook
 */
async function handlePaymentFailedWebhook(webhookData: any) {
  try {
    const { paymentID, errorMessage } = webhookData.data;

    const payment = await prisma.payment.findFirst({
      where: {
        bkashPaymentId: paymentID,
      },
    });

    if (payment && !['COMPLETED', 'REFUNDED'].includes(payment.status)) {
      await prisma.payment.update({
        where: { id: payment.id },
        data: {
          status: 'FAILED',
          failureReason: errorMessage || 'Payment failed (webhook)',
          gatewayResponse: webhookData,
        },
      });

      await prisma.order.update({
        where: { id: payment.orderId },
        data: {
          status: 'PAYMENT_FAILED',
          paymentStatus: 'FAILED',
        },
      });

      console.log('Payment failed via webhook:', { paymentID, errorMessage });
    }
  } catch (error) {
    console.error('Error handling payment failed webhook:', error);
  }
}

/**
 * Handle payment refunded webhook
 */
async function handlePaymentRefundedWebhook(webhookData: any) {
  try {
    const { paymentID, refundTrxID, refundAmount } = webhookData.data;

    const payment = await prisma.payment.findFirst({
      where: {
        bkashPaymentId: paymentID,
      },
    });

    if (payment) {
      await prisma.payment.update({
        where: { id: payment.id },
        data: {
          status: 'REFUNDED',
          refundAmount: parseFloat(refundAmount),
          refundReference: refundTrxID,
          refundedAt: new Date(),
          gatewayResponse: webhookData,
        },
      });

      await prisma.order.update({
        where: { id: payment.orderId },
        data: {
          status: 'REFUNDED',
          paymentStatus: 'REFUNDED',
        },
      });

      console.log('Payment refunded via webhook:', { paymentID, refundTrxID, refundAmount });
    }
  } catch (error) {
    console.error('Error handling payment refunded webhook:', error);
  }
}
