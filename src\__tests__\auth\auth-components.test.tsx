import { describe, it, expect, jest, beforeEach } from '@jest/globals';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useSession, signIn, signOut } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { LoginButton } from '@/components/auth/LoginButton';
import { UserMenu } from '@/components/auth/UserMenu';
import { AuthGuard } from '@/components/auth/AuthGuard';

// Mock NextAuth
jest.mock('next-auth/react');
const mockUseSession = useSession as jest.MockedFunction<typeof useSession>;
const mockSignIn = signIn as jest.MockedFunction<typeof signIn>;
const mockSignOut = signOut as jest.MockedFunction<typeof signOut>;

// Mock Next.js router
jest.mock('next/navigation');
const mockUseRouter = useRouter as jest.MockedFunction<typeof useRouter>;
const mockPush = jest.fn();
const mockBack = jest.fn();

// Mock Framer Motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
  },
  AnimatePresence: ({ children }: any) => children,
}));

describe('Auth Components', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseRouter.mockReturnValue({
      push: mockPush,
      back: mockBack,
    } as any);
  });

  describe('LoginButton', () => {
    it('should show loading state when session is loading', () => {
      mockUseSession.mockReturnValue({
        data: null,
        status: 'loading',
      });

      render(<LoginButton />);
      expect(screen.getByText('Loading...')).toBeInTheDocument();
    });

    it('should show login button when not authenticated', () => {
      mockUseSession.mockReturnValue({
        data: null,
        status: 'unauthenticated',
      });

      render(<LoginButton />);
      expect(screen.getByText('Join the Style Club')).toBeInTheDocument();
    });

    it('should call signIn when login button is clicked', async () => {
      mockUseSession.mockReturnValue({
        data: null,
        status: 'unauthenticated',
      });

      render(<LoginButton />);
      
      const loginButton = screen.getByText('Join the Style Club');
      fireEvent.click(loginButton);

      await waitFor(() => {
        expect(mockSignIn).toHaveBeenCalledWith(undefined, { callbackUrl: '/' });
      });
    });

    it('should show UserMenu when authenticated', () => {
      const mockSession = {
        user: {
          id: 'user-1',
          name: 'Test User',
          email: '<EMAIL>',
          role: 'user' as const,
        },
        expires: '2024-12-31',
      };

      mockUseSession.mockReturnValue({
        data: mockSession,
        status: 'authenticated',
      });

      render(<LoginButton />);
      expect(screen.getByLabelText('User menu')).toBeInTheDocument();
    });
  });

  describe('UserMenu', () => {
    const mockSession = {
      user: {
        id: 'user-1',
        name: 'Test User',
        email: '<EMAIL>',
        role: 'user' as const,
      },
      expires: '2024-12-31',
    };

    it('should render user avatar and name', () => {
      mockUseSession.mockReturnValue({
        data: mockSession,
        status: 'authenticated',
      });

      render(<UserMenu />);
      expect(screen.getByText('Test User')).toBeInTheDocument();
      expect(screen.getByText('Member')).toBeInTheDocument();
    });

    it('should show admin badge for admin users', () => {
      const adminSession = {
        ...mockSession,
        user: { ...mockSession.user, role: 'admin' as const },
      };

      mockUseSession.mockReturnValue({
        data: adminSession,
        status: 'authenticated',
      });

      render(<UserMenu />);
      expect(screen.getByText('Admin')).toBeInTheDocument();
    });

    it('should call signOut when sign out is clicked', async () => {
      mockUseSession.mockReturnValue({
        data: mockSession,
        status: 'authenticated',
      });

      render(<UserMenu />);
      
      // Open menu
      const menuButton = screen.getByLabelText('User menu');
      fireEvent.click(menuButton);

      // Click sign out
      const signOutButton = screen.getByText('Sign Out');
      fireEvent.click(signOutButton);

      await waitFor(() => {
        expect(mockSignOut).toHaveBeenCalledWith({ callbackUrl: '/' });
      });
    });

    it('should return null when no session', () => {
      mockUseSession.mockReturnValue({
        data: null,
        status: 'unauthenticated',
      });

      const { container } = render(<UserMenu />);
      expect(container.firstChild).toBeNull();
    });
  });

  describe('AuthGuard', () => {
    it('should render children when authenticated', () => {
      const mockSession = {
        user: {
          id: 'user-1',
          name: 'Test User',
          email: '<EMAIL>',
          role: 'user' as const,
        },
        expires: '2024-12-31',
      };

      mockUseSession.mockReturnValue({
        data: mockSession,
        status: 'authenticated',
      });

      render(
        <AuthGuard>
          <div>Protected Content</div>
        </AuthGuard>
      );

      expect(screen.getByText('Protected Content')).toBeInTheDocument();
    });

    it('should show loading state when session is loading', () => {
      mockUseSession.mockReturnValue({
        data: null,
        status: 'loading',
      });

      render(
        <AuthGuard>
          <div>Protected Content</div>
        </AuthGuard>
      );

      expect(screen.getByText('Loading your style journey...')).toBeInTheDocument();
    });

    it('should redirect to sign in when not authenticated', () => {
      mockUseSession.mockReturnValue({
        data: null,
        status: 'unauthenticated',
      });

      // Mock window.location
      Object.defineProperty(window, 'location', {
        value: {
          pathname: '/protected',
          search: '',
        },
        writable: true,
      });

      render(
        <AuthGuard>
          <div>Protected Content</div>
        </AuthGuard>
      );

      expect(mockPush).toHaveBeenCalledWith('/auth/signin?callbackUrl=%2Fprotected');
    });

    it('should show unauthorized page for non-admin accessing admin content', () => {
      const mockSession = {
        user: {
          id: 'user-1',
          name: 'Test User',
          email: '<EMAIL>',
          role: 'user' as const,
        },
        expires: '2024-12-31',
      };

      mockUseSession.mockReturnValue({
        data: mockSession,
        status: 'authenticated',
      });

      render(
        <AuthGuard requireAdmin>
          <div>Admin Content</div>
        </AuthGuard>
      );

      expect(screen.getByText('Admin Access Required')).toBeInTheDocument();
    });

    it('should render children for admin accessing admin content', () => {
      const mockSession = {
        user: {
          id: 'admin-1',
          name: 'Admin User',
          email: '<EMAIL>',
          role: 'admin' as const,
        },
        expires: '2024-12-31',
      };

      mockUseSession.mockReturnValue({
        data: mockSession,
        status: 'authenticated',
      });

      render(
        <AuthGuard requireAdmin>
          <div>Admin Content</div>
        </AuthGuard>
      );

      expect(screen.getByText('Admin Content')).toBeInTheDocument();
    });
  });
});
