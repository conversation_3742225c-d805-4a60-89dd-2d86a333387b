// Payment and Logistics Types for Ottiq

export interface CheckoutSession {
  id: string;
  orderId: string;
  userId: string;
  status: CheckoutStatus;
  totalAmount: number;
  currency: string;
  paymentMethod?: string;
  
  // Checkout Data
  shippingAddress: CheckoutAddress;
  billingAddress?: CheckoutAddress;
  giftMessage?: string;
  isGift: boolean;
  giftRecipientName?: string;
  giftRecipientEmail?: string;
  
  // Payment Intent
  paymentIntentId?: string;
  paymentIntentSecret?: string;
  
  // Expiration
  expiresAt: Date;
  createdAt: Date;
  updatedAt: Date;
}

export type CheckoutStatus = 
  | 'PENDING' 
  | 'PAYMENT_REQUIRED' 
  | 'PAYMENT_PROCESSING' 
  | 'COMPLETED' 
  | 'EXPIRED' 
  | 'CANCELLED';

export interface CheckoutAddress {
  name: string;
  phone: string;
  email?: string;
  street: string;
  area?: string;
  city: string;
  district: string;
  postalCode?: string;
  country: string;
  isDefault?: boolean;
}

// Checkout Flow Types
export interface CheckoutStep {
  id: string;
  name: string;
  title: string;
  description: string;
  isComplete: boolean;
  isActive: boolean;
  canSkip: boolean;
  emotionalMessage?: string;
}

export interface CheckoutFlow {
  steps: CheckoutStep[];
  currentStepIndex: number;
  isComplete: boolean;
  canProceed: boolean;
  totalSteps: number;
}

// Payment Method Types
export interface PaymentMethod {
  id: string;
  type: PaymentMethodType;
  name: string;
  displayName: string;
  description: string;
  icon: string;
  isEnabled: boolean;
  isDefault: boolean;
  processingFee?: number;
  processingFeeType?: 'fixed' | 'percentage';
  minAmount?: number;
  maxAmount?: number;
  supportedCurrencies: string[];
  emotionalBenefit?: string; // "Instant and secure"
  trustIndicators?: string[]; // ["SSL Secured", "Bank Grade Security"]
}

export type PaymentMethodType = 
  | 'bkash' 
  | 'nagad' 
  | 'rocket' 
  | 'card' 
  | 'bank_transfer' 
  | 'cash_on_delivery';

// Shipping Types
export interface ShippingMethod {
  id: string;
  name: string;
  displayName: string;
  description: string;
  cost: number;
  estimatedDays: string; // "3-5 days"
  isEnabled: boolean;
  isDefault: boolean;
  zones: string[];
  icon?: string;
  emotionalBenefit?: string; // "Get it fast, wear it sooner"
}

export interface ShippingZone {
  id: string;
  name: string;
  displayName: string;
  districts: string[];
  baseCost: number;
  freeShippingThreshold?: number;
  estimatedDays: string;
  isEnabled: boolean;
}

// Order Summary Types
export interface OrderSummary {
  subtotal: number;
  shippingCost: number;
  processingFee: number;
  discount: number;
  tax: number;
  total: number;
  currency: string;
  
  // Breakdown for transparency
  breakdown: OrderBreakdownItem[];
  
  // Emotional messaging
  valueMessage?: string; // "You're getting premium quality for less"
  savingsMessage?: string; // "You saved ৳200 with free shipping!"
}

export interface OrderBreakdownItem {
  id: string;
  label: string;
  amount: number;
  type: 'positive' | 'negative' | 'neutral';
  description?: string;
  isHighlighted?: boolean;
}

// Validation Types
export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

export interface CheckoutValidation {
  isValid: boolean;
  errors: ValidationError[];
  warnings: string[];
}

// Analytics Types
export interface CheckoutAnalytics {
  sessionId: string;
  userId?: string;
  events: CheckoutEvent[];
  startTime: Date;
  endTime?: Date;
  completionRate: number;
  dropOffStep?: string;
}

export interface CheckoutEvent {
  type: CheckoutEventType;
  step: string;
  timestamp: Date;
  data?: any;
}

export type CheckoutEventType = 
  | 'step_viewed' 
  | 'step_completed' 
  | 'payment_method_selected' 
  | 'shipping_method_selected' 
  | 'address_entered' 
  | 'payment_initiated' 
  | 'payment_completed' 
  | 'payment_failed' 
  | 'checkout_abandoned';

// Gift Message Types
export interface GiftMessageTemplate {
  id: string;
  category: string;
  occasion: string;
  message: string;
  tone: 'formal' | 'casual' | 'romantic' | 'friendly' | 'professional';
  isPopular: boolean;
}

// Discount and Coupon Types
export interface Coupon {
  id: string;
  code: string;
  type: CouponType;
  value: number;
  minOrderAmount?: number;
  maxDiscount?: number;
  isActive: boolean;
  validFrom: Date;
  validUntil: Date;
  usageLimit?: number;
  usedCount: number;
  description: string;
  emotionalMessage?: string; // "You deserve this discount!"
}

export type CouponType = 'percentage' | 'fixed' | 'free_shipping';

// Error Handling Types
export interface PaymentError {
  code: string;
  message: string;
  type: PaymentErrorType;
  retryable: boolean;
  userMessage: string; // User-friendly message
  supportMessage?: string; // Message to show with support contact
}

export type PaymentErrorType = 
  | 'network_error' 
  | 'payment_declined' 
  | 'insufficient_funds' 
  | 'invalid_payment_method' 
  | 'payment_timeout' 
  | 'gateway_error' 
  | 'validation_error';

// Success Types
export interface PaymentSuccess {
  paymentId: string;
  transactionId: string;
  orderId: string;
  amount: number;
  currency: string;
  method: string;
  timestamp: Date;
  confirmationMessage: string;
  nextSteps: string[];
  emotionalMessage: string; // "Your dream design is on its way!"
}
