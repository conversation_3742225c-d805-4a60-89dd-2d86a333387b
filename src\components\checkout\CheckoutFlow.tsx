'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { ShippingForm } from './ShippingForm';
import { PaymentMethodSelection } from './PaymentMethodSelection';
import { OrderReview } from './OrderReview';
import { CheckoutStep } from '@/types/payment';

interface CheckoutFlowProps {
  step: CheckoutStep;
  orderData: any;
  onStepComplete: (stepId: string, data: any) => void;
  onStepBack: () => void;
  onCheckoutComplete: (paymentData: any) => void;
  canGoBack: boolean;
}

export function CheckoutFlow({
  step,
  orderData,
  onStepComplete,
  onStepBack,
  onCheckoutComplete,
  canGoBack,
}: CheckoutFlowProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleStepSubmit = async (data: any) => {
    try {
      setIsLoading(true);
      setError(null);

      // Validate step data based on step type
      if (step.id === 'shipping') {
        await validateShippingData(data);
      } else if (step.id === 'payment') {
        await validatePaymentData(data);
      }

      // Complete the step
      onStepComplete(step.id, data);
    } catch (error) {
      console.error('Step submission error:', error);
      setError(error instanceof Error ? error.message : 'An error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleFinalSubmit = async (data: any) => {
    try {
      setIsLoading(true);
      setError(null);
      await onCheckoutComplete(data);
    } catch (error) {
      console.error('Checkout completion error:', error);
      setError(error instanceof Error ? error.message : 'Checkout failed');
    } finally {
      setIsLoading(false);
    }
  };

  const renderStepContent = () => {
    switch (step.id) {
      case 'shipping':
        return (
          <ShippingForm
            initialData={orderData.shipping}
            onSubmit={handleStepSubmit}
            onBack={canGoBack ? onStepBack : undefined}
            isLoading={isLoading}
            error={error}
          />
        );

      case 'payment':
        return (
          <PaymentMethodSelection
            orderTotal={orderData.totalAmount}
            currency={orderData.currency}
            shippingAddress={orderData.shipping?.address}
            onSubmit={handleStepSubmit}
            onBack={canGoBack ? onStepBack : undefined}
            isLoading={isLoading}
            error={error}
          />
        );

      case 'review':
        return (
          <OrderReview
            order={orderData}
            shippingData={orderData.shipping}
            paymentData={orderData.payment}
            onSubmit={handleFinalSubmit}
            onBack={canGoBack ? onStepBack : undefined}
            isLoading={isLoading}
            error={error}
          />
        );

      default:
        return (
          <div className="text-center py-8">
            <p className="text-gray-500">Unknown step: {step.id}</p>
          </div>
        );
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4 }}
      className="space-y-6"
    >
      {renderStepContent()}
    </motion.div>
  );
}

// Validation functions
async function validateShippingData(data: any): Promise<void> {
  const required = ['name', 'phone', 'street', 'city', 'district'];
  const missing = required.filter(field => !data.address?.[field]);
  
  if (missing.length > 0) {
    throw new Error(`Missing required fields: ${missing.join(', ')}`);
  }

  // Validate phone number format (Bangladesh)
  const phoneRegex = /^(\+88)?01[3-9]\d{8}$/;
  if (!phoneRegex.test(data.address.phone.replace(/\s/g, ''))) {
    throw new Error('Please enter a valid Bangladesh phone number');
  }

  // Validate shipping method
  if (!data.method || !['standard', 'express'].includes(data.method)) {
    throw new Error('Please select a valid shipping method');
  }
}

async function validatePaymentData(data: any): Promise<void> {
  if (!data.method) {
    throw new Error('Please select a payment method');
  }

  const validMethods = ['bkash', 'nagad', 'rocket', 'card', 'cash_on_delivery'];
  if (!validMethods.includes(data.method)) {
    throw new Error('Invalid payment method selected');
  }

  // Validate bKash phone number if bKash is selected
  if (data.method === 'bkash') {
    if (!data.customerPhone) {
      throw new Error('bKash phone number is required');
    }
    
    const phoneRegex = /^(\+88)?01[3-9]\d{8}$/;
    if (!phoneRegex.test(data.customerPhone.replace(/\s/g, ''))) {
      throw new Error('Please enter a valid bKash phone number');
    }
  }
}
