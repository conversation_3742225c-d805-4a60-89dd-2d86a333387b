import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';

const prisma = new PrismaClient();

// Address validation schema
const AddressSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  phone: z.string().min(10, 'Valid phone number is required'),
  email: z.string().email().optional(),
  street: z.string().min(1, 'Street address is required'),
  area: z.string().optional(),
  city: z.string().min(1, 'City is required'),
  district: z.string().min(1, 'District is required'),
  postalCode: z.string().optional(),
  country: z.string().default('Bangladesh'),
});

// Order creation schema
const CreateOrderSchema = z.object({
  items: z.array(z.object({
    productId: z.string().min(1, 'Product ID is required'),
    customizationId: z.string().optional(),
    quantity: z.number().int().min(1, 'Quantity must be at least 1'),
    price: z.number().positive('Price must be positive'),
  })).min(1, 'At least one item is required'),
  shippingAddress: AddressSchema,
  billingAddress: AddressSchema.optional(),
  shippingMethod: z.string().default('standard'),
  giftMessage: z.string().optional(),
  isGift: z.boolean().default(false),
  giftRecipientName: z.string().optional(),
  giftRecipientEmail: z.string().email().optional(),
  couponCode: z.string().optional(),
});

/**
 * POST /api/orders - Create a new order
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          error: 'Authentication required',
          message: 'You must be logged in to create an order',
        },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = CreateOrderSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid request data',
          details: validationResult.error.issues.map((err) => ({
            field: err.path.join('.'),
            message: err.message,
          })),
        },
        { status: 400 }
      );
    }

    const orderData = validationResult.data;

    // Calculate order totals
    const subtotal = orderData.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    
    // Calculate shipping cost based on method and location
    const shippingCost = calculateShippingCost(orderData.shippingMethod, orderData.shippingAddress.district, subtotal);
    
    // Apply coupon discount if provided
    let discount = 0;
    if (orderData.couponCode) {
      discount = await calculateCouponDiscount(orderData.couponCode, subtotal);
    }
    
    const totalAmount = subtotal + shippingCost - discount;

    // Generate unique order number
    const orderNumber = generateOrderNumber();

    // Create order in database
    const order = await prisma.order.create({
      data: {
        orderNumber,
        status: 'PENDING',
        totalAmount,
        currency: 'BDT',
        shippingAddress: orderData.shippingAddress,
        billingAddress: orderData.billingAddress || orderData.shippingAddress,
        giftMessage: orderData.giftMessage,
        isGift: orderData.isGift,
        giftRecipientName: orderData.giftRecipientName,
        giftRecipientEmail: orderData.giftRecipientEmail,
        shippingCost,
        paymentStatus: 'PENDING',
        userId: session.user.id,
        items: {
          create: orderData.items.map(item => ({
            quantity: item.quantity,
            price: item.price,
            productId: item.productId,
            customizationId: item.customizationId,
            productSnapshot: {}, // Will be populated with product details
          })),
        },
      },
      include: {
        items: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                category: true,
                heroImage: true,
              },
            },
            customization: {
              select: {
                id: true,
                name: true,
                previewImage: true,
              },
            },
          },
        },
      },
    });

    // Create shipping info
    if (order.id) {
      await prisma.shippingInfo.create({
        data: {
          method: orderData.shippingMethod,
          cost: shippingCost,
          zone: getShippingZone(orderData.shippingAddress.district),
          recipientName: orderData.shippingAddress.name,
          recipientPhone: orderData.shippingAddress.phone,
          address: orderData.shippingAddress,
          status: 'PENDING',
          estimatedDelivery: calculateEstimatedDelivery(orderData.shippingMethod),
          orderId: order.id,
        },
      });
    }

    console.log('Order created successfully:', {
      orderId: order.id,
      orderNumber: order.orderNumber,
      userId: session.user.id,
      totalAmount: order.totalAmount,
      itemCount: order.items.length,
    });

    return NextResponse.json({
      success: true,
      data: {
        orderId: order.id,
        orderNumber: order.orderNumber,
        status: order.status,
        totalAmount: order.totalAmount,
        currency: order.currency,
        shippingCost,
        discount,
        subtotal,
        items: order.items,
        estimatedDelivery: calculateEstimatedDelivery(orderData.shippingMethod),
      },
      message: 'Order created successfully',
    });
  } catch (error) {
    console.error('Order creation error:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'ORDER_CREATION_FAILED',
        message: 'Failed to create order. Please try again.',
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/orders - Get user's orders
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          error: 'Authentication required',
        },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');

    const skip = (page - 1) * limit;

    const where = {
      userId: session.user.id,
      ...(status && { status }),
    };

    const [orders, total] = await Promise.all([
      prisma.order.findMany({
        where,
        include: {
          items: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  category: true,
                  heroImage: true,
                },
              },
              customization: {
                select: {
                  id: true,
                  name: true,
                  previewImage: true,
                },
              },
            },
          },
          payments: {
            select: {
              id: true,
              status: true,
              method: true,
              bkashTransactionId: true,
              createdAt: true,
            },
          },
          shipping: {
            select: {
              status: true,
              trackingNumber: true,
              estimatedDelivery: true,
              actualDelivery: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
      }),
      prisma.order.count({ where }),
    ]);

    return NextResponse.json({
      success: true,
      data: orders,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Get orders error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to retrieve orders',
      },
      { status: 500 }
    );
  }
}

// Helper functions
function generateOrderNumber(): string {
  const timestamp = Date.now();
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `ORD-${timestamp}-${random}`;
}

function calculateShippingCost(method: string, district: string, subtotal: number): number {
  // Free shipping threshold
  if (subtotal >= 1000) {
    return 0;
  }

  const shippingRates: Record<string, Record<string, number>> = {
    standard: {
      dhaka: 60,
      chittagong: 80,
      sylhet: 100,
      rajshahi: 100,
      khulna: 100,
      barisal: 120,
      rangpur: 120,
      mymensingh: 100,
      default: 120,
    },
    express: {
      dhaka: 120,
      chittagong: 150,
      sylhet: 180,
      default: 200,
    },
  };

  const methodRates = shippingRates[method] || shippingRates.standard;
  return methodRates[district.toLowerCase()] || methodRates.default;
}

function getShippingZone(district: string): string {
  const zones: Record<string, string> = {
    dhaka: 'dhaka',
    chittagong: 'chittagong',
    sylhet: 'sylhet',
    rajshahi: 'rajshahi',
    khulna: 'khulna',
    barisal: 'barisal',
    rangpur: 'rangpur',
    mymensingh: 'mymensingh',
  };

  return zones[district.toLowerCase()] || 'other';
}

function calculateEstimatedDelivery(method: string): Date {
  const now = new Date();
  const deliveryDays = method === 'express' ? 2 : 5;
  return new Date(now.getTime() + deliveryDays * 24 * 60 * 60 * 1000);
}

async function calculateCouponDiscount(couponCode: string, subtotal: number): Promise<number> {
  // Placeholder for coupon logic
  // In a real implementation, you would validate the coupon against a database
  return 0;
}
