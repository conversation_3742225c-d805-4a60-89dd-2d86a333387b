'use client';

import { useSearchParams } from 'next/navigation';
import { motion } from 'framer-motion';
import { Container, Section } from '@/components/layout';
import { Button } from '@/components/ui';
import { Logo } from '@/components/brand/Logo';
import Link from 'next/link';

export default function AuthErrorPage() {
  const searchParams = useSearchParams();
  const error = searchParams.get('error');

  const getErrorDetails = (error: string | null) => {
    switch (error) {
      case 'Configuration':
        return {
          title: 'Server Configuration Issue',
          message: 'There\'s a temporary issue with our authentication system. Our team has been notified and is working on a fix.',
          suggestion: 'Please try again in a few minutes, or contact support if the issue persists.',
          canRetry: true,
        };
      case 'AccessDenied':
        return {
          title: 'Access Not Granted',
          message: 'You chose not to grant access to your account, or there was an issue with the permissions.',
          suggestion: 'To join the Ottiq style community, we need basic access to create your account. Please try signing in again and grant the necessary permissions.',
          canRetry: true,
        };
      case 'Verification':
        return {
          title: 'Verification Failed',
          message: 'We couldn\'t verify your account with the authentication provider.',
          suggestion: 'This might be a temporary issue. Please try signing in again, or try a different sign-in method.',
          canRetry: true,
        };
      case 'Default':
      default:
        return {
          title: 'Something Went Wrong',
          message: 'We encountered an unexpected issue while trying to sign you in to the style club.',
          suggestion: 'Don\'t worry - this happens sometimes. Please try again, and if the issue continues, our support team is here to help.',
          canRetry: true,
        };
    }
  };

  const errorDetails = getErrorDetails(error);

  return (
    <div className="min-h-screen bg-gradient-to-br from-warm-50 via-primary-50 to-accent-50 flex items-center justify-center p-4">
      <Section variant="primary" padding="none" className="w-full max-w-md">
        <Container>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="card-premium p-8 text-center space-y-8"
          >
            {/* Logo */}
            <Logo size="lg" showTagline={false} />

            {/* Error Icon */}
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center"
            >
              <svg
                className="w-8 h-8 text-red-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
            </motion.div>

            {/* Error Details */}
            <div className="space-y-4">
              <h1 className="text-2xl font-bold text-gray-900">
                {errorDetails.title}
              </h1>
              
              <p className="text-gray-600 leading-relaxed">
                {errorDetails.message}
              </p>
              
              <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
                <p className="text-blue-800 text-sm leading-relaxed">
                  💡 {errorDetails.suggestion}
                </p>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="space-y-4">
              {errorDetails.canRetry && (
                <Link href="/auth/signin">
                  <Button size="lg" fullWidth>
                    Try Signing In Again
                  </Button>
                </Link>
              )}
              
              <Link href="/">
                <Button variant="outline" size="lg" fullWidth>
                  Return to Homepage
                </Button>
              </Link>
            </div>

            {/* Support Information */}
            <div className="pt-6 border-t border-gray-200">
              <p className="text-sm text-gray-500 mb-3">
                Still having trouble?
              </p>
              <div className="space-y-2 text-sm">
                <p className="text-gray-600">
                  Our support team is here to help you join the style community.
                </p>
                <div className="flex flex-col sm:flex-row gap-2 justify-center">
                  <a
                    href="mailto:<EMAIL>"
                    className="text-primary-600 hover:text-primary-700 underline"
                  >
                    Email Support
                  </a>
                  <span className="hidden sm:inline text-gray-400">•</span>
                  <Link
                    href="/help"
                    className="text-primary-600 hover:text-primary-700 underline"
                  >
                    Help Center
                  </Link>
                </div>
              </div>
            </div>

            {/* Error Code for Support */}
            {error && (
              <div className="pt-4 border-t border-gray-100">
                <p className="text-xs text-gray-400">
                  Error Code: {error}
                </p>
              </div>
            )}
          </motion.div>
        </Container>
      </Section>
    </div>
  );
}
