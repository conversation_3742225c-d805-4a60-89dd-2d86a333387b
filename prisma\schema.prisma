// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User Management
model User {
  id            String    @id @default(cuid())
  email         String    @unique
  name          String?
  image         String?
  emailVerified DateTime?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // User preferences for personalization
  stylePreferences Json?    // Store style quiz results, preferred colors, etc.
  sizeProfile      Json?    // Store body measurements, preferred fit

  // Privacy & Consent Management
  aiTryOnConsent      Boolean   @default(false)  // User consent for AI try-on
  aiTryOnConsentDate  DateTime? // When consent was given
  privacyPolicyAccepted Boolean @default(false)  // Privacy policy acceptance
  privacyPolicyDate   DateTime? // When privacy policy was accepted
  dataRetentionDays   Int       @default(30)     // How long to keep user images (days)

  // Usage Tracking for Limits
  dailyTryOnCount     Int       @default(0)      // Current day's try-on count
  lastTryOnDate       DateTime? // Last try-on date for daily reset
  totalTryOnCount     Int       @default(0)      // Total lifetime try-ons

  // Relations
  accounts      Account[]
  sessions      Session[]
  designs       Design[]
  orders        Order[]
  payments      Payment[]
  customizations Customization[]
  aiTryOnJobs   AiTryOnJob[]
  privacyRequests PrivacyRequest[]

  @@map("users")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verificationtokens")
}

// Product Catalog
model Product {
  id          String   @id @default(cuid())
  name        String
  description String   @db.Text
  category    String   // e.g., "t-shirt", "hoodie", "dress"
  subcategory String?  // e.g., "oversized", "fitted", "cropped"

  // Emotional & Lifestyle Appeal
  moodTags    String[] // e.g., ["confident", "playful", "sophisticated"]
  lifestyleTags String[] // e.g., ["streetwear", "athleisure", "workwear"]

  // Imagery for emotional connection
  heroImage     String   // Main product image
  lifestyleImages Json   // Array of lifestyle images with context
  // Example: [{"url": "...", "context": "street", "mood": "confident", "model": "diverse"}]

  // Pricing
  basePrice   Decimal  @db.Decimal(10, 2)
  currency    String   @default("USD")

  // Product status
  isActive    Boolean  @default(true)
  isFeatured  Boolean  @default(false)

  // AI Try-On Admin Controls
  aiTryOnEnabled Boolean @default(true)  // Admin toggle for AI try-on availability
  aiTryOnPriority Int    @default(1)     // Priority for fallback queue (1=high, 5=low)

  // Metadata
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  variants    ProductVariant[]
  templates   Template[]
  orderItems  OrderItem[]
  customizations Customization[]

  @@map("products")
}

// Product Variants (Size, Color combinations)
model ProductVariant {
  id        String  @id @default(cuid())
  sku       String  @unique

  // Relations
  productId String
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)
  sizeId    String
  size      Size    @relation(fields: [sizeId], references: [id])
  colorId   String
  color     Color   @relation(fields: [colorId], references: [id])
  fabricId  String?
  fabric    Fabric? @relation(fields: [fabricId], references: [id])

  // Variant-specific data
  price     Decimal? @db.Decimal(10, 2) // Override base price if needed
  stock     Int      @default(0)
  isActive  Boolean  @default(true)

  // Metadata
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("product_variants")
}

// Fabrics with emotional appeal
model Fabric {
  id          String   @id @default(cuid())
  name        String   @unique
  description String   @db.Text

  // Emotional attributes
  feelTags    String[] // e.g., ["soft", "luxurious", "breathable", "cozy"]
  careTags    String[] // e.g., ["easy-care", "wrinkle-free", "sustainable"]

  // Technical specs
  composition String   // e.g., "100% Organic Cotton"
  weight      Int?     // GSM (grams per square meter)
  stretch     String?  // e.g., "4-way stretch", "no stretch"

  // Imagery
  textureImage String?
  swatchImage  String?

  // Pricing impact
  priceModifier Decimal @default(0) @db.Decimal(5, 2) // Additional cost

  // Status
  isActive    Boolean  @default(true)

  // Metadata
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  variants    ProductVariant[]

  @@map("fabrics")
}

// Colors with emotional associations
model Color {
  id          String   @id @default(cuid())
  name        String   @unique
  hexCode     String   @unique

  // Emotional attributes
  moodTags    String[] // e.g., ["energetic", "calming", "bold", "sophisticated"]
  seasonTags  String[] // e.g., ["spring", "summer", "fall", "winter"]

  // Color family for recommendations
  colorFamily String   // e.g., "warm", "cool", "neutral", "earth"

  // Imagery
  swatchImage String?

  // Status
  isActive    Boolean  @default(true)
  isPopular   Boolean  @default(false)

  // Metadata
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  variants    ProductVariant[]

  @@map("colors")
}

// Sizes with fit descriptions
model Size {
  id          String   @id @default(cuid())
  name        String   @unique // e.g., "XS", "S", "M", "L", "XL"
  displayName String   // e.g., "Extra Small", "Small"

  // Size specifications
  measurements Json    // Store chest, waist, hip, length measurements
  fitType     String   // e.g., "regular", "slim", "oversized", "relaxed"

  // Emotional appeal
  fitDescription String @db.Text // e.g., "Perfect for a confident, tailored look"

  // Ordering
  sortOrder   Int      @default(0)

  // Status
  isActive    Boolean  @default(true)

  // Metadata
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  variants    ProductVariant[]

  @@map("sizes")
}

// Design Templates with emotional styling
model Template {
  id          String   @id @default(cuid())
  name        String
  description String   @db.Text

  // Emotional & Style Attributes
  moodTag     String   // e.g., "Bold Streetwear", "Minimalist Chic", "Vintage Rebel"
  styleKeywords String[] // e.g., ["urban", "edgy", "confident"]
  targetAudience String // e.g., "young professionals", "creative artists", "fitness enthusiasts"

  // Template data
  designData  Json     // Konva/Fabric.js template data
  previewImage String  // Template preview image

  // Lifestyle context
  lifestyleContext String[] // e.g., ["work", "weekend", "date night", "gym"]

  // Relations
  productId   String
  product     Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  // Usage tracking
  usageCount  Int      @default(0)

  // Status
  isActive    Boolean  @default(true)
  isFeatured  Boolean  @default(false)

  // Metadata
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  customizations Customization[]

  @@map("templates")
}

// User Customizations
model Customization {
  id          String   @id @default(cuid())
  name        String
  description String?

  // Customization data
  designData  Json     // Store the customized design
  previewImage String? // Generated preview

  // Relations
  userId      String
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  productId   String
  product     Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
  templateId  String?
  template    Template? @relation(fields: [templateId], references: [id])

  // Status
  status      CustomizationStatus @default(DRAFT)

  // Metadata
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  orderItems  OrderItem[]
  aiTryOnJobs AiTryOnJob[]

  @@map("customizations")
}

enum CustomizationStatus {
  DRAFT
  COMPLETED
  ORDERED
  ARCHIVED
}

// Pricing Rules for dynamic pricing
model PriceRule {
  id          String   @id @default(cuid())
  name        String
  description String?

  // Rule conditions
  conditions  Json     // Store complex pricing conditions
  // Example: {"minQuantity": 5, "fabricType": "premium", "customizationComplexity": "high"}

  // Price modification
  modifier    Decimal  @db.Decimal(5, 2) // Percentage or fixed amount
  modifierType String  // "percentage" or "fixed"

  // Validity
  validFrom   DateTime?
  validUntil  DateTime?

  // Status
  isActive    Boolean  @default(true)
  priority    Int      @default(0) // Higher priority rules apply first

  // Metadata
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("price_rules")
}

// AI Try-On Jobs
model AiTryOnJob {
  id          String   @id @default(cuid())

  // Job details
  status      AiTryOnStatus @default(PENDING)
  jobId       String?  // External AI service job ID

  // Input data
  userPhotoUrl String  // User's photo for try-on
  customizationId String
  customization Customization @relation(fields: [customizationId], references: [id], onDelete: Cascade)

  // Output data
  resultImageUrl String? // Generated try-on image
  confidence    Float?   // AI confidence score
  processingTime Int?    // Processing time in seconds

  // Privacy & Data Management
  userPhotoHash   String?   // Hash of user photo for duplicate detection
  scheduledDeletion DateTime? // When images should be auto-deleted
  isDeleted       Boolean   @default(false) // Soft delete flag
  deletedAt       DateTime? // When images were deleted
  consentVersion  String?   // Version of consent when job was created

  // Error handling
  errorMessage  String?
  retryCount    Int      @default(0)

  // Relations
  userId      String
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  fallbackQueue AiTryOnFallbackQueue? // Optional fallback queue entry

  // Metadata
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("ai_try_on_jobs")
}

enum AiTryOnStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  CANCELLED
  QUEUED_FOR_FALLBACK
  FALLBACK_PROCESSING
  FALLBACK_COMPLETED
}

// Fallback Queue for Manual Processing
model AiTryOnFallbackQueue {
  id          String   @id @default(cuid())

  // Related AI Try-On Job
  aiTryOnJobId String  @unique
  aiTryOnJob   AiTryOnJob @relation(fields: [aiTryOnJobId], references: [id], onDelete: Cascade)

  // Queue Management
  status      FallbackQueueStatus @default(PENDING)
  priority    Int      @default(1) // 1=high, 5=low priority
  assignedTo  String?  // Admin user ID who claimed this job

  // Processing Details
  colabNotebookUrl String? // URL to Colab notebook for manual processing
  processingNotes  String? @db.Text // Admin notes during processing
  estimatedCompletion DateTime? // When admin expects to complete

  // Retry Information
  originalError String? @db.Text // Original error that caused fallback
  retryCount    Int     @default(0)
  lastRetryAt   DateTime?

  // Metadata
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("ai_try_on_fallback_queue")
}

enum FallbackQueueStatus {
  PENDING      // Waiting for admin to claim
  CLAIMED      // Admin has claimed the job
  PROCESSING   // Admin is working on it
  COMPLETED    // Successfully processed
  FAILED       // Failed even in manual processing
  CANCELLED    // Cancelled by admin
}

// Design System (Updated)
model Design {
  id          String      @id @default(cuid())
  title       String
  description String?
  imageUrl    String?
  designData  Json        // Store Konva/Fabric.js design data
  category    String
  tags        String[]
  isPublic    Boolean     @default(false)
  status      DesignStatus @default(DRAFT)

  // Metadata
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  userId      String
  user        User        @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("designs")
}

enum DesignStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
}

// Order Management (Updated)
model Order {
  id          String      @id @default(cuid())
  orderNumber String      @unique
  status      OrderStatus @default(PENDING)
  totalAmount Decimal     @db.Decimal(10, 2)
  currency    String      @default("BDT")

  // Customer Info
  shippingAddress Json
  billingAddress  Json?

  // Gift Message Support
  giftMessage     String?  @db.Text
  isGift          Boolean  @default(false)
  giftRecipientName String?
  giftRecipientEmail String?

  // Order tracking
  trackingNumber String?
  estimatedDelivery DateTime?
  actualDelivery    DateTime?
  shippingCost      Decimal? @db.Decimal(10, 2)

  // Payment Information
  paymentStatus   PaymentStatus @default(PENDING)
  paymentMethod   String?       // e.g., "bkash", "card", "cash_on_delivery"

  // Metadata
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  userId      String
  user        User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  items       OrderItem[]
  payments    Payment[]
  shipping    ShippingInfo?

  @@map("orders")
}

model OrderItem {
  id       String  @id @default(cuid())
  quantity Int     @default(1)
  price    Decimal @db.Decimal(10, 2)

  // Product details at time of order
  productSnapshot Json // Store product details as they were when ordered

  // Relations
  orderId         String
  order           Order         @relation(fields: [orderId], references: [id], onDelete: Cascade)
  productId       String
  product         Product       @relation(fields: [productId], references: [id])
  customizationId String?
  customization   Customization? @relation(fields: [customizationId], references: [id])

  @@map("order_items")
}

enum OrderStatus {
  PENDING
  PAYMENT_PENDING
  PAYMENT_FAILED
  CONFIRMED
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
  REFUNDED
}

enum PaymentStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  CANCELLED
  REFUNDED
  PARTIALLY_REFUNDED
}

// Payment Management
model Payment {
  id          String        @id @default(cuid())

  // Payment Details
  amount      Decimal       @db.Decimal(10, 2)
  currency    String        @default("BDT")
  status      PaymentStatus @default(PENDING)
  method      String        // e.g., "bkash", "card", "cash_on_delivery"

  // bKash Specific Fields
  bkashPaymentId    String?   // bKash payment ID
  bkashTransactionId String?  // bKash transaction ID (trxID)
  bkashInvoiceNumber String?  // Merchant invoice number
  bkashAgreementId   String?  // For tokenized payments

  // Payment Gateway Response
  gatewayResponse   Json?     // Store full gateway response
  gatewayReference  String?   // Gateway reference number

  // Refund Information
  refundAmount      Decimal?  @db.Decimal(10, 2)
  refundReason      String?   @db.Text
  refundedAt        DateTime?
  refundReference   String?

  // Failure Information
  failureReason     String?   @db.Text
  failureCode       String?

  // Metadata
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  orderId     String
  order       Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)
  userId      String
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("payments")
}

// Shipping Information
model ShippingInfo {
  id          String   @id @default(cuid())

  // Shipping Details
  method      String   // e.g., "standard", "express", "same_day"
  cost        Decimal  @db.Decimal(10, 2)
  zone        String   // Shipping zone (dhaka, chittagong, etc.)

  // Tracking Information
  trackingNumber    String?
  carrierName       String?   // e.g., "Sundarban Courier", "SA Paribahan"
  trackingUrl       String?

  // Delivery Information
  estimatedDelivery DateTime?
  actualDelivery    DateTime?
  deliveryAttempts  Int       @default(0)
  deliveryNotes     String?   @db.Text

  // Address Information (duplicated from order for tracking)
  recipientName     String
  recipientPhone    String
  address           Json      // Full address details

  // Status Tracking
  status            ShippingStatus @default(PENDING)
  statusHistory     Json?     // Array of status changes with timestamps

  // Metadata
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  orderId     String   @unique
  order       Order    @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@map("shipping_info")
}

enum ShippingStatus {
  PENDING
  CONFIRMED
  PICKED_UP
  IN_TRANSIT
  OUT_FOR_DELIVERY
  DELIVERED
  FAILED_DELIVERY
  RETURNED
  CANCELLED
}

// Privacy Request Management for GDPR Compliance
model PrivacyRequest {
  id          String   @id @default(cuid())

  // Request details
  type        PrivacyRequestType
  status      PrivacyRequestStatus @default(PENDING)
  description String?  @db.Text

  // User information
  userId      String
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Processing details
  processedBy String?  // Admin user ID who processed the request
  processedAt DateTime?
  completedAt DateTime?

  // Data export (for data portability requests)
  exportFileUrl String? // URL to exported data file
  exportFileSize Int?   // Size of export file in bytes

  // Deletion details (for erasure requests)
  itemsDeleted Json?   // List of deleted items with details

  // Admin notes
  adminNotes  String?  @db.Text

  // Metadata
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("privacy_requests")
}

enum PrivacyRequestType {
  DATA_EXPORT     // GDPR Article 20 - Data Portability
  DATA_DELETION   // GDPR Article 17 - Right to Erasure
  DATA_CORRECTION // GDPR Article 16 - Right to Rectification
  CONSENT_WITHDRAWAL // Withdraw AI try-on consent
}

enum PrivacyRequestStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  REJECTED
  CANCELLED
}
