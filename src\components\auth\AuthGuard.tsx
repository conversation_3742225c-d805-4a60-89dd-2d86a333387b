'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect, ReactNode } from 'react';
import { motion } from 'framer-motion';
import { Container, Section } from '@/components/layout';
import { Button } from '@/components/ui';
import { Logo } from '@/components/brand/Logo';

interface AuthGuardProps {
  children: ReactNode;
  requireAuth?: boolean;
  requireAdmin?: boolean;
  fallbackUrl?: string;
  loadingComponent?: ReactNode;
}

export function AuthGuard({
  children,
  requireAuth = true,
  requireAdmin = false,
  fallbackUrl = '/auth/signin',
  loadingComponent,
}: AuthGuardProps) {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (status === 'loading') return; // Still loading

    if (requireAuth && !session) {
      // Redirect to sign in with current URL as callback
      const currentUrl = window.location.pathname + window.location.search;
      router.push(`${fallbackUrl}?callbackUrl=${encodeURIComponent(currentUrl)}`);
      return;
    }

    if (requireAdmin && session?.user?.role !== 'admin') {
      // Redirect to unauthorized page or home
      router.push('/unauthorized');
      return;
    }
  }, [session, status, requireAuth, requireAdmin, router, fallbackUrl]);

  // Show loading state
  if (status === 'loading') {
    if (loadingComponent) {
      return <>{loadingComponent}</>;
    }

    return (
      <div className="min-h-screen bg-gradient-to-br from-warm-50 to-amber-50 flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center space-y-6"
        >
          <Logo size="lg" showTagline={false} />
          <div className="flex items-center justify-center gap-2">
            <div className="w-2 h-2 bg-primary-500 rounded-full animate-bounce"></div>
            <div className="w-2 h-2 bg-warm-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
            <div className="w-2 h-2 bg-accent-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
          </div>
          <p className="text-gray-600">Loading your style journey...</p>
        </motion.div>
      </div>
    );
  }

  // Show unauthorized state for admin-only content
  if (requireAdmin && session?.user?.role !== 'admin') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-warm-50 to-amber-50 flex items-center justify-center p-4">
        <Section variant="primary" padding="none" className="w-full max-w-md">
          <Container>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="card-premium p-8 text-center space-y-6"
            >
              <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
                <svg
                  className="w-8 h-8 text-red-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                  />
                </svg>
              </div>
              
              <div className="space-y-3">
                <h1 className="text-2xl font-bold text-gray-900">
                  Admin Access Required
                </h1>
                <p className="text-gray-600">
                  This area is reserved for style club administrators. 
                  You don't have the necessary permissions to access this content.
                </p>
              </div>

              <Button
                onClick={() => router.push('/')}
                size="lg"
                fullWidth
              >
                Return to Homepage
              </Button>
            </motion.div>
          </Container>
        </Section>
      </div>
    );
  }

  // Show unauthenticated state
  if (requireAuth && !session) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-warm-50 to-amber-50 flex items-center justify-center p-4">
        <Section variant="primary" padding="none" className="w-full max-w-md">
          <Container>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="card-premium p-8 text-center space-y-6"
            >
              <Logo size="lg" showTagline={false} />
              
              <div className="space-y-3">
                <h1 className="text-2xl font-bold text-gradient-primary">
                  Join the Style Club
                </h1>
                <p className="text-gray-600">
                  Sign in to access your personal style journey and unlock 
                  all the creative tools waiting for you.
                </p>
              </div>

              <Button
                onClick={() => router.push(fallbackUrl)}
                size="lg"
                fullWidth
              >
                Sign In to Continue
              </Button>
            </motion.div>
          </Container>
        </Section>
      </div>
    );
  }

  // Render children if all checks pass
  return <>{children}</>;
}
