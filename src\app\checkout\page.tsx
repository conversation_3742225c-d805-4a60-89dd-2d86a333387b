'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { motion, AnimatePresence } from 'framer-motion';
import { CheckoutFlow } from '@/components/checkout/CheckoutFlow';
import { CheckoutSummary } from '@/components/checkout/CheckoutSummary';
import { CheckoutProgress } from '@/components/checkout/CheckoutProgress';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { EmotionalMessage } from '@/components/ui/EmotionalMessage';
import { CheckoutStep, CheckoutFlow as CheckoutFlowType } from '@/types/payment';

export default function CheckoutPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { data: session, status } = useSession();
  
  const [checkoutFlow, setCheckoutFlow] = useState<CheckoutFlowType | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [orderData, setOrderData] = useState<any>(null);

  // Get order ID from URL params
  const orderId = searchParams.get('orderId');

  useEffect(() => {
    if (status === 'loading') return;
    
    if (!session) {
      router.push('/auth/signin?callbackUrl=/checkout');
      return;
    }

    if (!orderId) {
      router.push('/create');
      return;
    }

    initializeCheckout();
  }, [session, status, orderId]);

  const initializeCheckout = async () => {
    try {
      setIsLoading(true);
      
      // Fetch order details
      const orderResponse = await fetch(`/api/orders/${orderId}`);
      if (!orderResponse.ok) {
        throw new Error('Order not found');
      }
      
      const orderResult = await orderResponse.json();
      if (!orderResult.success) {
        throw new Error(orderResult.error || 'Failed to load order');
      }

      setOrderData(orderResult.data);

      // Initialize checkout flow
      const steps: CheckoutStep[] = [
        {
          id: 'shipping',
          name: 'shipping',
          title: 'Where should we send your creation?',
          description: 'Let us know where to deliver your personalized masterpiece',
          isComplete: false,
          isActive: true,
          canSkip: false,
          emotionalMessage: 'Your dream design is almost ready to come home with you! ✨',
        },
        {
          id: 'payment',
          name: 'payment',
          title: 'Choose your payment method',
          description: 'Secure and trusted payment options for your peace of mind',
          isComplete: false,
          isActive: false,
          canSkip: false,
          emotionalMessage: 'You\'re just one step away from wearing your imagination! 💫',
        },
        {
          id: 'review',
          name: 'review',
          title: 'Review your order',
          description: 'Take a final look at your personalized creation',
          isComplete: false,
          isActive: false,
          canSkip: false,
          emotionalMessage: 'This is it – your unique style is about to become reality! 🎨',
        },
      ];

      setCheckoutFlow({
        steps,
        currentStepIndex: 0,
        isComplete: false,
        canProceed: false,
        totalSteps: steps.length,
      });

    } catch (error) {
      console.error('Checkout initialization error:', error);
      setError(error instanceof Error ? error.message : 'Failed to initialize checkout');
    } finally {
      setIsLoading(false);
    }
  };

  const handleStepComplete = (stepId: string, data: any) => {
    if (!checkoutFlow) return;

    const updatedSteps = checkoutFlow.steps.map(step => {
      if (step.id === stepId) {
        return { ...step, isComplete: true, isActive: false };
      }
      return step;
    });

    const currentIndex = checkoutFlow.currentStepIndex;
    const nextIndex = Math.min(currentIndex + 1, checkoutFlow.totalSteps - 1);
    
    // Activate next step
    if (nextIndex < checkoutFlow.totalSteps) {
      updatedSteps[nextIndex].isActive = true;
    }

    const isComplete = nextIndex >= checkoutFlow.totalSteps - 1 && updatedSteps[nextIndex].isComplete;

    setCheckoutFlow({
      ...checkoutFlow,
      steps: updatedSteps,
      currentStepIndex: nextIndex,
      isComplete,
      canProceed: updatedSteps[currentIndex].isComplete,
    });

    // Store step data
    setOrderData((prev: any) => ({
      ...prev,
      [stepId]: data,
    }));
  };

  const handleStepBack = () => {
    if (!checkoutFlow || checkoutFlow.currentStepIndex === 0) return;

    const prevIndex = checkoutFlow.currentStepIndex - 1;
    const updatedSteps = checkoutFlow.steps.map((step, index) => ({
      ...step,
      isActive: index === prevIndex,
    }));

    setCheckoutFlow({
      ...checkoutFlow,
      steps: updatedSteps,
      currentStepIndex: prevIndex,
      isComplete: false,
      canProceed: true,
    });
  };

  const handleCheckoutComplete = async (paymentData: any) => {
    try {
      // Create payment
      const paymentResponse = await fetch('/api/payments/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          orderId: orderData.id,
          amount: orderData.totalAmount,
          currency: orderData.currency,
          paymentMethod: paymentData.method,
          customerPhone: paymentData.customerPhone,
          returnUrl: `${window.location.origin}/checkout/success`,
          cancelUrl: `${window.location.origin}/checkout/cancel`,
        }),
      });

      const paymentResult = await paymentResponse.json();
      
      if (paymentResult.success) {
        if (paymentResult.data.paymentUrl) {
          // Redirect to payment gateway
          window.location.href = paymentResult.data.paymentUrl;
        } else {
          // For COD or other methods, redirect to success page
          router.push(`/checkout/success?orderId=${orderData.id}`);
        }
      } else {
        throw new Error(paymentResult.message || 'Payment creation failed');
      }
    } catch (error) {
      console.error('Checkout completion error:', error);
      setError(error instanceof Error ? error.message : 'Checkout failed');
    }
  };

  if (status === 'loading' || isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-warm-50 to-primary-50 flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-warm-600 font-medium">Preparing your checkout experience...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-warm-50 to-primary-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto px-4">
          <div className="bg-red-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Oops! Something went wrong</h1>
          <p className="text-gray-600 mb-6">{error}</p>
          <button
            onClick={() => router.push('/create')}
            className="bg-primary-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-primary-700 transition-colors"
          >
            Start Over
          </button>
        </div>
      </div>
    );
  }

  if (!checkoutFlow || !orderData) {
    return null;
  }

  const currentStep = checkoutFlow.steps[checkoutFlow.currentStepIndex];

  return (
    <div className="min-h-screen bg-gradient-to-br from-warm-50 to-primary-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-warm-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.back()}
                className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Checkout</h1>
                <p className="text-sm text-gray-600">Order #{orderData.orderNumber}</p>
              </div>
            </div>
            <div className="hidden md:block">
              <CheckoutProgress flow={checkoutFlow} />
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Progress */}
      <div className="md:hidden bg-white border-b border-warm-200 px-4 py-3">
        <CheckoutProgress flow={checkoutFlow} />
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Checkout Flow */}
          <div className="lg:col-span-2">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentStep.id}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
                className="bg-white rounded-xl shadow-sm border border-warm-200 overflow-hidden"
              >
                {/* Step Header */}
                <div className="bg-gradient-to-r from-primary-500 to-warm-500 px-6 py-4">
                  <h2 className="text-xl font-bold text-white">{currentStep.title}</h2>
                  <p className="text-primary-100 mt-1">{currentStep.description}</p>
                </div>

                {/* Emotional Message */}
                {currentStep.emotionalMessage && (
                  <div className="px-6 py-4 bg-warm-50 border-b border-warm-200">
                    <EmotionalMessage 
                      message={currentStep.emotionalMessage}
                      tone="inspiring"
                    />
                  </div>
                )}

                {/* Step Content */}
                <div className="p-6">
                  <CheckoutFlow
                    step={currentStep}
                    orderData={orderData}
                    onStepComplete={handleStepComplete}
                    onStepBack={handleStepBack}
                    onCheckoutComplete={handleCheckoutComplete}
                    canGoBack={checkoutFlow.currentStepIndex > 0}
                  />
                </div>
              </motion.div>
            </AnimatePresence>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <div className="sticky top-8">
              <CheckoutSummary 
                order={orderData}
                currentStep={currentStep.id}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
