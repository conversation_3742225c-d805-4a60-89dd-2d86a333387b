import { test, expect } from '@playwright/test';

test.describe('Authentication Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Start from the homepage
    await page.goto('/');
  });

  test('should display sign-in page with branded content', async ({ page }) => {
    await page.goto('/auth/signin');

    // Check for branded elements
    await expect(page.getByRole('heading', { name: 'Join the Style Club' })).toBeVisible();
    await expect(page.getByText('Welcome to a community where your imagination becomes wearable art')).toBeVisible();
    
    // Check for OAuth buttons
    await expect(page.getByRole('button', { name: 'Continue with Google' })).toBeVisible();
    await expect(page.getByRole('button', { name: 'Continue with Facebook' })).toBeVisible();
    
    // Check for benefits section
    await expect(page.getByText('AI-powered try-on experiences')).toBeVisible();
    await expect(page.getByText('Custom design tools & templates')).toBeVisible();
    await expect(page.getByText('Your personal style journey')).toBeVisible();
    
    // Check for privacy links
    await expect(page.getByRole('link', { name: 'Privacy Policy' })).toBeVisible();
    await expect(page.getByRole('link', { name: 'Terms of Service' })).toBeVisible();
  });

  test('should display error page with helpful information', async ({ page }) => {
    await page.goto('/auth/error?error=AccessDenied');

    // Check for error content
    await expect(page.getByRole('heading', { name: 'Access Not Granted' })).toBeVisible();
    await expect(page.getByText('You chose not to grant access to your account')).toBeVisible();
    
    // Check for action buttons
    await expect(page.getByRole('link', { name: 'Try Signing In Again' })).toBeVisible();
    await expect(page.getByRole('link', { name: 'Return to Homepage' })).toBeVisible();
    
    // Check for support information
    await expect(page.getByRole('link', { name: 'Email Support' })).toBeVisible();
    await expect(page.getByRole('link', { name: 'Help Center' })).toBeVisible();
  });

  test('should redirect to sign-in when accessing protected routes', async ({ page }) => {
    // Try to access a protected route
    await page.goto('/create');
    
    // Should be redirected to sign-in page
    await expect(page).toHaveURL(/\/auth\/signin/);
    await expect(page.getByRole('heading', { name: 'Join the Style Club' })).toBeVisible();
  });

  test('should display unauthorized page for admin routes', async ({ page }) => {
    await page.goto('/unauthorized');

    // Check for unauthorized content
    await expect(page.getByRole('heading', { name: 'Access Restricted' })).toBeVisible();
    await expect(page.getByText('This area of the style club is reserved for administrators only')).toBeVisible();
    
    // Check for action buttons
    await expect(page.getByRole('button', { name: 'Go Back' })).toBeVisible();
    await expect(page.getByRole('link', { name: 'Return to Homepage' })).toBeVisible();
    
    // Check for support information
    await expect(page.getByRole('link', { name: 'Contact Support' })).toBeVisible();
  });

  test('should handle different error types correctly', async ({ page }) => {
    const errorTypes = [
      { error: 'Configuration', title: 'Server Configuration Issue' },
      { error: 'Verification', title: 'Verification Failed' },
      { error: 'Default', title: 'Something Went Wrong' },
    ];

    for (const { error, title } of errorTypes) {
      await page.goto(`/auth/error?error=${error}`);
      await expect(page.getByRole('heading', { name: title })).toBeVisible();
    }
  });

  test('should have proper mobile responsiveness', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    await page.goto('/auth/signin');
    
    // Check that content is properly displayed on mobile
    await expect(page.getByRole('heading', { name: 'Join the Style Club' })).toBeVisible();
    await expect(page.getByRole('button', { name: 'Continue with Google' })).toBeVisible();
    await expect(page.getByRole('button', { name: 'Continue with Facebook' })).toBeVisible();
    
    // Check that buttons are touch-friendly (at least 44px height)
    const googleButton = page.getByRole('button', { name: 'Continue with Google' });
    const buttonBox = await googleButton.boundingBox();
    expect(buttonBox?.height).toBeGreaterThanOrEqual(44);
  });

  test('should have proper accessibility features', async ({ page }) => {
    await page.goto('/auth/signin');
    
    // Check for proper heading hierarchy
    const h1 = page.getByRole('heading', { level: 1 });
    await expect(h1).toBeVisible();
    
    // Check for proper button labels
    await expect(page.getByRole('button', { name: 'Continue with Google' })).toBeVisible();
    await expect(page.getByRole('button', { name: 'Continue with Facebook' })).toBeVisible();
    
    // Check for proper link text
    await expect(page.getByRole('link', { name: 'Privacy Policy' })).toBeVisible();
    await expect(page.getByRole('link', { name: 'Terms of Service' })).toBeVisible();
  });

  test('should preserve callback URL in sign-in flow', async ({ page }) => {
    // Navigate to sign-in with callback URL
    await page.goto('/auth/signin?callbackUrl=%2Fcreate');
    
    // Check that the page loads correctly
    await expect(page.getByRole('heading', { name: 'Join the Style Club' })).toBeVisible();
    
    // The callback URL should be preserved (we can't test the actual OAuth flow in E2E)
    expect(page.url()).toContain('callbackUrl=%2Fcreate');
  });

  test('should display loading states appropriately', async ({ page }) => {
    await page.goto('/auth/signin');
    
    // Mock a slow network to test loading states
    await page.route('**/*', route => {
      setTimeout(() => route.continue(), 100);
    });
    
    // The buttons should be clickable (not in loading state initially)
    const googleButton = page.getByRole('button', { name: 'Continue with Google' });
    await expect(googleButton).toBeEnabled();
    
    const facebookButton = page.getByRole('button', { name: 'Continue with Facebook' });
    await expect(facebookButton).toBeEnabled();
  });
});
