import type { Metada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import { SessionProvider } from '@/components/providers/SessionProvider';
import { Navigation } from '@/components/layout';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Ottiq — Wear Your Imagination',
  description: 'Create custom fashion that expresses your unique style. AI-powered design meets emotional connection.',
  keywords: ['custom fashion', 'AI design', 'personalized clothing', 'style expression'],
  authors: [{ name: 'Ottiq Team' }],
  creator: '<PERSON>tti<PERSON>',
  publisher: 'Ottiq',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://ottiq.com'),
  openGraph: {
    title: 'Ottiq — Wear Your Imagination',
    description: 'Create custom fashion that expresses your unique style',
    url: 'https://ottiq.com',
    siteName: 'Ottiq',
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Ottiq — Wear Your Imagination',
    description: 'Create custom fashion that expresses your unique style',
    creator: '@ottiq',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className="scroll-smooth">
      <body className={`${inter.className} antialiased bg-gradient-to-br from-warm-50 to-amber-50 min-h-screen`}>
        <SessionProvider>
          <div id="root">
            <Navigation />
            {children}
          </div>
        </SessionProvider>
      </body>
    </html>
  );
}
