import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { PaymentService } from '@/lib/services/payment';
import { z } from 'zod';

// Request validation schema
const CreatePaymentSchema = z.object({
  orderId: z.string().min(1, 'Order ID is required'),
  amount: z.number().positive('Amount must be positive'),
  currency: z.string().default('BDT'),
  paymentMethod: z.enum(['bkash', 'nagad', 'rocket', 'card', 'cash_on_delivery']),
  customerPhone: z.string().optional(),
  returnUrl: z.string().url().optional(),
  cancelUrl: z.string().url().optional(),
  metadata: z.record(z.any()).optional(),
});

/**
 * POST /api/payments/create - Create a new payment
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          error: 'Authentication required',
          message: 'You must be logged in to create a payment',
        },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = CreatePaymentSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid request data',
          details: validationResult.error.issues.map((err) => ({
            field: err.path.join('.'),
            message: err.message,
          })),
        },
        { status: 400 }
      );
    }

    const paymentData = validationResult.data;

    // Create payment using PaymentService
    const paymentService = new PaymentService();
    const result = await paymentService.createPayment({
      ...paymentData,
      userId: session.user.id,
    });

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: {
          paymentId: result.payment?.id,
          paymentUrl: result.paymentUrl,
          status: result.payment?.status,
          method: result.payment?.method,
          amount: result.payment?.amount,
          currency: result.payment?.currency,
          ...result.data,
        },
        message: 'Payment created successfully',
      });
    } else {
      const statusCode = result.error?.type === 'validation_error' ? 400 : 500;
      return NextResponse.json(
        {
          success: false,
          error: result.error?.code || 'PAYMENT_CREATION_FAILED',
          message: result.error?.userMessage || 'Failed to create payment',
          retryable: result.error?.retryable || false,
        },
        { status: statusCode }
      );
    }
  } catch (error) {
    console.error('Payment creation API error:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'INTERNAL_SERVER_ERROR',
        message: 'An unexpected error occurred while creating the payment',
        retryable: true,
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/payments/create - Get payment creation form data
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          error: 'Authentication required',
        },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const orderId = searchParams.get('orderId');

    if (!orderId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Order ID is required',
        },
        { status: 400 }
      );
    }

    // Get available payment methods
    const paymentMethods = [
      {
        id: 'bkash',
        name: 'bKash',
        displayName: 'bKash Mobile Banking',
        description: 'Pay securely with your bKash account',
        icon: '/icons/bkash.svg',
        isEnabled: true,
        isDefault: true,
        emotionalBenefit: 'Quick and trusted by millions',
        trustIndicators: ['SSL Secured', 'Bank Grade Security', 'Instant Processing'],
        minAmount: 1,
        maxAmount: 25000,
        processingFee: 0,
        processingFeeType: 'fixed' as const,
      },
      {
        id: 'cash_on_delivery',
        name: 'cash_on_delivery',
        displayName: 'Cash on Delivery',
        description: 'Pay when you receive your order',
        icon: '/icons/cod.svg',
        isEnabled: true,
        isDefault: false,
        emotionalBenefit: 'Pay only when you love it',
        trustIndicators: ['No advance payment', 'Inspect before paying'],
        processingFee: 0,
        processingFeeType: 'fixed' as const,
      },
    ];

    // Get shipping methods for Bangladesh
    const shippingMethods = [
      {
        id: 'standard',
        name: 'Standard Delivery',
        description: 'Regular delivery within Bangladesh',
        cost: 60,
        estimatedDays: '3-5 days',
        isEnabled: true,
        isDefault: true,
        zones: ['dhaka', 'chittagong', 'sylhet', 'rajshahi', 'khulna', 'barisal', 'rangpur', 'mymensingh'],
        emotionalBenefit: 'Reliable delivery to your doorstep',
      },
      {
        id: 'express',
        name: 'Express Delivery',
        description: 'Faster delivery for Dhaka and major cities',
        cost: 120,
        estimatedDays: '1-2 days',
        isEnabled: true,
        isDefault: false,
        zones: ['dhaka', 'chittagong', 'sylhet'],
        emotionalBenefit: 'Get your style faster',
      },
    ];

    return NextResponse.json({
      success: true,
      data: {
        paymentMethods,
        shippingMethods,
        supportedCurrencies: ['BDT'],
        defaultCurrency: 'BDT',
        config: {
          minOrderAmount: 100,
          maxOrderAmount: 50000,
          freeShippingThreshold: 1000,
        },
      },
    });
  } catch (error) {
    console.error('Payment form data API error:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to load payment form data',
      },
      { status: 500 }
    );
  }
}
