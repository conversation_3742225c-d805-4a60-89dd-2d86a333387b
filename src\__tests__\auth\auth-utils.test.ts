import { describe, it, expect, jest, beforeEach } from '@jest/globals';
import { NextRequest } from 'next/server';
import { getServerSession } from 'next-auth';
import {
  requireAuth,
  requireAdmin,
  canAccessResource,
  hasPermission,
  isAuthenticated,
  isAdmin,
} from '@/lib/auth-utils';

// Mock NextAuth
jest.mock('next-auth');
const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;

// Mock request
const createMockRequest = () => {
  return new NextRequest('http://localhost:3000/test');
};

describe('Auth Utils', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('requireAuth', () => {
    it('should return user when authenticated', async () => {
      const mockUser = {
        id: 'user-1',
        email: '<EMAIL>',
        role: 'user' as const,
      };

      mockGetServerSession.mockResolvedValue({
        user: mockUser,
        expires: '2024-12-31',
      });

      const request = createMockRequest();
      const result = await requireAuth(request);

      expect(result).toEqual(mockUser);
    });

    it('should return 401 response when not authenticated', async () => {
      mockGetServerSession.mockResolvedValue(null);

      const request = createMockRequest();
      const result = await requireAuth(request);

      expect(result).toHaveProperty('status', 401);
    });
  });

  describe('requireAdmin', () => {
    it('should return user when user is admin', async () => {
      const mockAdmin = {
        id: 'admin-1',
        email: '<EMAIL>',
        role: 'admin' as const,
      };

      mockGetServerSession.mockResolvedValue({
        user: mockAdmin,
        expires: '2024-12-31',
      });

      const request = createMockRequest();
      const result = await requireAdmin(request);

      expect(result).toEqual(mockAdmin);
    });

    it('should return 401 when not authenticated', async () => {
      mockGetServerSession.mockResolvedValue(null);

      const request = createMockRequest();
      const result = await requireAdmin(request);

      expect(result).toHaveProperty('status', 401);
    });

    it('should return 403 when user is not admin', async () => {
      const mockUser = {
        id: 'user-1',
        email: '<EMAIL>',
        role: 'user' as const,
      };

      mockGetServerSession.mockResolvedValue({
        user: mockUser,
        expires: '2024-12-31',
      });

      const request = createMockRequest();
      const result = await requireAdmin(request);

      expect(result).toHaveProperty('status', 403);
    });
  });

  describe('canAccessResource', () => {
    it('should allow admin to access any resource', async () => {
      const mockAdmin = {
        id: 'admin-1',
        email: '<EMAIL>',
        role: 'admin' as const,
      };

      mockGetServerSession.mockResolvedValue({
        user: mockAdmin,
        expires: '2024-12-31',
      });

      const result = await canAccessResource('user-123');
      expect(result).toBe(true);
    });

    it('should allow user to access their own resource', async () => {
      const mockUser = {
        id: 'user-1',
        email: '<EMAIL>',
        role: 'user' as const,
      };

      mockGetServerSession.mockResolvedValue({
        user: mockUser,
        expires: '2024-12-31',
      });

      const result = await canAccessResource('user-1');
      expect(result).toBe(true);
    });

    it('should deny user access to other users resources', async () => {
      const mockUser = {
        id: 'user-1',
        email: '<EMAIL>',
        role: 'user' as const,
      };

      mockGetServerSession.mockResolvedValue({
        user: mockUser,
        expires: '2024-12-31',
      });

      const result = await canAccessResource('user-2');
      expect(result).toBe(false);
    });

    it('should deny access when not authenticated', async () => {
      mockGetServerSession.mockResolvedValue(null);

      const result = await canAccessResource('user-1');
      expect(result).toBe(false);
    });
  });

  describe('hasPermission', () => {
    it('should grant all permissions to admin', async () => {
      const mockAdmin = {
        id: 'admin-1',
        email: '<EMAIL>',
        role: 'admin' as const,
      };

      mockGetServerSession.mockResolvedValue({
        user: mockAdmin,
        expires: '2024-12-31',
      });

      const result = await hasPermission('any_permission');
      expect(result).toBe(true);
    });

    it('should grant specific permissions to user', async () => {
      const mockUser = {
        id: 'user-1',
        email: '<EMAIL>',
        role: 'user' as const,
      };

      mockGetServerSession.mockResolvedValue({
        user: mockUser,
        expires: '2024-12-31',
      });

      const result = await hasPermission('create_design');
      expect(result).toBe(true);
    });

    it('should deny permissions when not authenticated', async () => {
      mockGetServerSession.mockResolvedValue(null);

      const result = await hasPermission('create_design');
      expect(result).toBe(false);
    });
  });

  describe('isAuthenticated', () => {
    it('should return true when user is authenticated', async () => {
      const mockUser = {
        id: 'user-1',
        email: '<EMAIL>',
        role: 'user' as const,
      };

      mockGetServerSession.mockResolvedValue({
        user: mockUser,
        expires: '2024-12-31',
      });

      const result = await isAuthenticated();
      expect(result).toBe(true);
    });

    it('should return false when user is not authenticated', async () => {
      mockGetServerSession.mockResolvedValue(null);

      const result = await isAuthenticated();
      expect(result).toBe(false);
    });
  });

  describe('isAdmin', () => {
    it('should return true when user is admin', async () => {
      const mockAdmin = {
        id: 'admin-1',
        email: '<EMAIL>',
        role: 'admin' as const,
      };

      mockGetServerSession.mockResolvedValue({
        user: mockAdmin,
        expires: '2024-12-31',
      });

      const result = await isAdmin();
      expect(result).toBe(true);
    });

    it('should return false when user is not admin', async () => {
      const mockUser = {
        id: 'user-1',
        email: '<EMAIL>',
        role: 'user' as const,
      };

      mockGetServerSession.mockResolvedValue({
        user: mockUser,
        expires: '2024-12-31',
      });

      const result = await isAdmin();
      expect(result).toBe(false);
    });

    it('should return false when user is not authenticated', async () => {
      mockGetServerSession.mockResolvedValue(null);

      const result = await isAdmin();
      expect(result).toBe(false);
    });
  });
});
