
import { signIn, useSession } from 'next-auth/react';
import { Button } from '@/components/ui';
import { UserMenu } from './UserMenu';

interface LoginButtonProps {
  variant?: 'primary' | 'secondary' | 'ghost' | 'outline';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  fullWidth?: boolean;
  callbackUrl?: string;
  className?: string;
}

export function LoginButton({ variant = 'primary', size = 'md' }: LoginButtonProps) {
  const { data: session, status } = useSession();

  if (session) {
    return <UserMenu />;
  }

  return (
    <Button onClick={() => signIn(undefined, { callbackUrl })}>
      Join the Style Club
    </Button>
  );
}

