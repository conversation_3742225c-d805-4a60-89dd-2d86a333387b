/**
 * Payment Service
 * 
 * High-level payment service that orchestrates payment operations
 * across different payment methods and integrates with order management.
 */

import { PrismaClient } from '@prisma/client';
import { getBkashService } from './bkash';
import { 
  Payment, 
  PaymentStatus, 
  Order,
  CheckoutSession,
  PaymentMethod,
  PaymentError,
  PaymentSuccess 
} from '@/types/payment';

const prisma = new PrismaClient();

export interface CreatePaymentRequest {
  orderId: string;
  userId: string;
  amount: number;
  currency: string;
  paymentMethod: string;
  customerPhone?: string;
  returnUrl?: string;
  cancelUrl?: string;
  metadata?: Record<string, any>;
}

export interface PaymentResult {
  success: boolean;
  payment?: Payment;
  paymentUrl?: string;
  error?: PaymentError;
  data?: any;
}

export class PaymentService {
  private bkashService = getBkashService();

  /**
   * Create a new payment
   */
  async createPayment(request: CreatePaymentRequest): Promise<PaymentResult> {
    try {
      // Validate the order exists and belongs to the user
      const order = await prisma.order.findFirst({
        where: {
          id: request.orderId,
          userId: request.userId,
        },
        include: {
          items: true,
        },
      });

      if (!order) {
        return {
          success: false,
          error: {
            code: 'ORDER_NOT_FOUND',
            message: 'Order not found or access denied',
            type: 'validation_error',
            retryable: false,
            userMessage: 'The order you are trying to pay for was not found.',
          },
        };
      }

      // Check if order is in correct status for payment
      if (!['PENDING', 'PAYMENT_PENDING', 'PAYMENT_FAILED'].includes(order.status)) {
        return {
          success: false,
          error: {
            code: 'INVALID_ORDER_STATUS',
            message: `Order status ${order.status} is not valid for payment`,
            type: 'validation_error',
            retryable: false,
            userMessage: 'This order cannot be paid for at this time.',
          },
        };
      }

      // Validate amount matches order total
      if (Math.abs(request.amount - Number(order.totalAmount)) > 0.01) {
        return {
          success: false,
          error: {
            code: 'AMOUNT_MISMATCH',
            message: 'Payment amount does not match order total',
            type: 'validation_error',
            retryable: false,
            userMessage: 'The payment amount is incorrect. Please refresh and try again.',
          },
        };
      }

      // Create payment record
      const payment = await prisma.payment.create({
        data: {
          amount: request.amount,
          currency: request.currency,
          status: 'PENDING',
          method: request.paymentMethod,
          orderId: request.orderId,
          userId: request.userId,
          gatewayResponse: request.metadata || {},
        },
      });

      // Update order status
      await prisma.order.update({
        where: { id: request.orderId },
        data: { 
          status: 'PAYMENT_PENDING',
          paymentStatus: 'PENDING',
          paymentMethod: request.paymentMethod,
        },
      });

      // Process payment based on method
      switch (request.paymentMethod.toLowerCase()) {
        case 'bkash':
          return await this.processBkashPayment(payment, request);
        
        case 'cash_on_delivery':
          return await this.processCashOnDelivery(payment);
        
        default:
          return {
            success: false,
            error: {
              code: 'UNSUPPORTED_PAYMENT_METHOD',
              message: `Payment method ${request.paymentMethod} is not supported`,
              type: 'validation_error',
              retryable: false,
              userMessage: 'The selected payment method is not available.',
            },
          };
      }
    } catch (error) {
      console.error('Payment creation error:', error);
      return {
        success: false,
        error: {
          code: 'PAYMENT_CREATION_FAILED',
          message: error instanceof Error ? error.message : 'Unknown error',
          type: 'gateway_error',
          retryable: true,
          userMessage: 'Unable to create payment. Please try again.',
        },
      };
    }
  }

  /**
   * Process bKash payment
   */
  private async processBkashPayment(payment: any, request: CreatePaymentRequest): Promise<PaymentResult> {
    try {
      if (!this.bkashService.validateAmount(request.amount)) {
        return {
          success: false,
          error: {
            code: 'INVALID_AMOUNT',
            message: 'Amount is outside bKash limits',
            type: 'validation_error',
            retryable: false,
            userMessage: 'The payment amount is not valid for bKash payments.',
          },
        };
      }

      const invoiceNumber = this.bkashService.generateInvoiceNumber(request.orderId);
      const callbackUrl = request.returnUrl || `${process.env.APP_URL}/api/payments/bkash/callback`;

      const bkashPaymentRequest = {
        mode: '0011', // Tokenized checkout
        payerReference: request.customerPhone || '01700000000',
        callbackURL: callbackUrl,
        amount: this.bkashService.formatAmount(request.amount),
        currency: 'BDT',
        intent: 'sale',
        merchantInvoiceNumber: invoiceNumber,
      };

      const bkashResponse = await this.bkashService.createPayment(bkashPaymentRequest);

      // Update payment with bKash details
      const updatedPayment = await prisma.payment.update({
        where: { id: payment.id },
        data: {
          bkashPaymentId: bkashResponse.paymentID,
          bkashInvoiceNumber: invoiceNumber,
          gatewayResponse: bkashResponse,
          status: 'PROCESSING',
        },
      });

      return {
        success: true,
        payment: updatedPayment as any,
        paymentUrl: bkashResponse.bkashURL,
        data: {
          paymentID: bkashResponse.paymentID,
          redirectUrl: bkashResponse.bkashURL,
        },
      };
    } catch (error) {
      console.error('bKash payment processing error:', error);
      
      // Update payment status to failed
      await prisma.payment.update({
        where: { id: payment.id },
        data: {
          status: 'FAILED',
          failureReason: error instanceof Error ? error.message : 'Unknown error',
        },
      });

      return {
        success: false,
        error: {
          code: 'BKASH_PAYMENT_FAILED',
          message: error instanceof Error ? error.message : 'bKash payment failed',
          type: 'gateway_error',
          retryable: true,
          userMessage: 'Unable to process bKash payment. Please try again.',
        },
      };
    }
  }

  /**
   * Process cash on delivery
   */
  private async processCashOnDelivery(payment: any): Promise<PaymentResult> {
    try {
      // For COD, we mark payment as pending and order as confirmed
      const updatedPayment = await prisma.payment.update({
        where: { id: payment.id },
        data: {
          status: 'PENDING',
        },
      });

      await prisma.order.update({
        where: { id: payment.orderId },
        data: {
          status: 'CONFIRMED',
          paymentStatus: 'PENDING',
        },
      });

      return {
        success: true,
        payment: updatedPayment as any,
        data: {
          message: 'Order confirmed. Payment will be collected on delivery.',
        },
      };
    } catch (error) {
      console.error('COD processing error:', error);
      return {
        success: false,
        error: {
          code: 'COD_PROCESSING_FAILED',
          message: error instanceof Error ? error.message : 'COD processing failed',
          type: 'gateway_error',
          retryable: true,
          userMessage: 'Unable to process cash on delivery order. Please try again.',
        },
      };
    }
  }

  /**
   * Execute bKash payment after user authorization
   */
  async executeBkashPayment(paymentID: string): Promise<PaymentResult> {
    try {
      // Find the payment record
      const payment = await prisma.payment.findFirst({
        where: {
          bkashPaymentId: paymentID,
        },
        include: {
          order: true,
        },
      });

      if (!payment) {
        return {
          success: false,
          error: {
            code: 'PAYMENT_NOT_FOUND',
            message: 'Payment record not found',
            type: 'validation_error',
            retryable: false,
            userMessage: 'Payment not found.',
          },
        };
      }

      // Execute payment with bKash
      const executeResponse = await this.bkashService.executePayment({ paymentID });

      if (this.bkashService.isPaymentSuccessful(executeResponse.transactionStatus)) {
        // Update payment as completed
        const updatedPayment = await prisma.payment.update({
          where: { id: payment.id },
          data: {
            status: 'COMPLETED',
            bkashTransactionId: executeResponse.trxID,
            gatewayResponse: executeResponse,
          },
        });

        // Update order as confirmed
        await prisma.order.update({
          where: { id: payment.orderId },
          data: {
            status: 'CONFIRMED',
            paymentStatus: 'COMPLETED',
          },
        });

        return {
          success: true,
          payment: updatedPayment as any,
          data: {
            transactionId: executeResponse.trxID,
            message: 'Payment completed successfully',
          },
        };
      } else {
        // Payment failed
        await prisma.payment.update({
          where: { id: payment.id },
          data: {
            status: 'FAILED',
            failureReason: `Transaction status: ${executeResponse.transactionStatus}`,
            gatewayResponse: executeResponse,
          },
        });

        await prisma.order.update({
          where: { id: payment.orderId },
          data: {
            status: 'PAYMENT_FAILED',
            paymentStatus: 'FAILED',
          },
        });

        return {
          success: false,
          error: {
            code: 'PAYMENT_EXECUTION_FAILED',
            message: `Payment execution failed: ${executeResponse.transactionStatus}`,
            type: 'payment_declined',
            retryable: true,
            userMessage: 'Payment was not successful. Please try again.',
          },
        };
      }
    } catch (error) {
      console.error('Payment execution error:', error);
      return {
        success: false,
        error: {
          code: 'PAYMENT_EXECUTION_ERROR',
          message: error instanceof Error ? error.message : 'Payment execution error',
          type: 'gateway_error',
          retryable: true,
          userMessage: 'Unable to complete payment. Please try again.',
        },
      };
    }
  }
}
