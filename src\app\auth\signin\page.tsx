'use client';

import { useState, useEffect } from 'react';
import { signIn, getSession } from 'next-auth/react';
import { useRouter, useSearchParams } from 'next/navigation';
import { motion } from 'framer-motion';
import { Container, Section } from '@/components/layout';
import { Button } from '@/components/ui';
import { Logo } from '@/components/brand/Logo';

export default function SignInPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isLoading, setIsLoading] = useState(false);
  const [loadingProvider, setLoadingProvider] = useState<string | null>(null);
  const callbackUrl = searchParams.get('callbackUrl') || '/';
  const error = searchParams.get('error');

  useEffect(() => {
    // Check if user is already signed in
    getSession().then((session) => {
      if (session) {
        router.push(callbackUrl);
      }
    });
  }, [router, callbackUrl]);

  const handleSignIn = async (provider: 'google' | 'facebook') => {
    setIsLoading(true);
    setLoadingProvider(provider);
    
    try {
      await signIn(provider, { 
        callbackUrl,
        redirect: true 
      });
    } catch (error) {
      console.error('Sign in error:', error);
    } finally {
      setIsLoading(false);
      setLoadingProvider(null);
    }
  };

  const getErrorMessage = (error: string | null) => {
    switch (error) {
      case 'OAuthSignin':
        return 'There was an issue connecting to the authentication service. Please try again.';
      case 'OAuthCallback':
        return 'There was an issue with the authentication callback. Please try again.';
      case 'OAuthCreateAccount':
        return 'Could not create your account. Please try again.';
      case 'EmailCreateAccount':
        return 'Could not create your account. Please try again.';
      case 'Callback':
        return 'There was an issue with authentication. Please try again.';
      case 'OAuthAccountNotLinked':
        return 'This email is already associated with another account. Please sign in with the original provider.';
      case 'EmailSignin':
        return 'Check your email for the sign in link.';
      case 'CredentialsSignin':
        return 'Sign in failed. Check the details you provided are correct.';
      case 'SessionRequired':
        return 'Please sign in to access this page.';
      default:
        return 'An unexpected error occurred. Please try again.';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-warm-50 via-primary-50 to-accent-50 flex items-center justify-center p-4">
      <Section variant="primary" padding="none" className="w-full max-w-md">
        <Container>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="card-premium p-8 text-center space-y-8"
          >
            {/* Logo and Welcome */}
            <div className="space-y-6">
              <Logo size="lg" showTagline={false} />
              
              <div className="space-y-3">
                <h1 className="text-3xl font-bold text-gradient-primary">
                  Join the Style Club
                </h1>
                <p className="text-gray-600 leading-relaxed">
                  Welcome to a community where your imagination becomes wearable art. 
                  Sign in to start creating fashion that tells your unique story.
                </p>
              </div>
            </div>

            {/* Error Message */}
            {error && (
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                className="bg-red-50 border border-red-200 rounded-xl p-4"
              >
                <p className="text-red-700 text-sm">
                  {getErrorMessage(error)}
                </p>
              </motion.div>
            )}

            {/* Social Sign In Buttons */}
            <div className="space-y-4">
              <Button
                onClick={() => handleSignIn('google')}
                disabled={isLoading}
                isLoading={loadingProvider === 'google'}
                variant="outline"
                size="lg"
                fullWidth
                leftIcon={
                  <svg className="w-5 h-5" viewBox="0 0 24 24">
                    <path
                      fill="currentColor"
                      d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                    />
                    <path
                      fill="currentColor"
                      d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                    />
                    <path
                      fill="currentColor"
                      d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                    />
                    <path
                      fill="currentColor"
                      d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                    />
                  </svg>
                }
              >
                Continue with Google
              </Button>

              <Button
                onClick={() => handleSignIn('facebook')}
                disabled={isLoading}
                isLoading={loadingProvider === 'facebook'}
                variant="outline"
                size="lg"
                fullWidth
                leftIcon={
                  <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                  </svg>
                }
              >
                Continue with Facebook
              </Button>
            </div>

            {/* Benefits */}
            <div className="pt-6 border-t border-gray-200">
              <p className="text-sm text-gray-500 mb-4">
                When you join, you'll unlock:
              </p>
              <div className="grid grid-cols-1 gap-3 text-sm text-gray-600">
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-primary-400 rounded-full"></div>
                  <span>AI-powered try-on experiences</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-warm-400 rounded-full"></div>
                  <span>Custom design tools & templates</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-accent-400 rounded-full"></div>
                  <span>Your personal style journey</span>
                </div>
              </div>
            </div>

            {/* Privacy Note */}
            <p className="text-xs text-gray-500 leading-relaxed">
              By signing in, you agree to our{' '}
              <a href="/privacy" className="text-primary-600 hover:text-primary-700 underline">
                Privacy Policy
              </a>{' '}
              and{' '}
              <a href="/terms" className="text-primary-600 hover:text-primary-700 underline">
                Terms of Service
              </a>
              . We respect your privacy and will never share your personal information.
            </p>
          </motion.div>
        </Container>
      </Section>
    </div>
  );
}
