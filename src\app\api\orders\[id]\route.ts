import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { OrderService } from '@/lib/services/order';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';

const prisma = new PrismaClient();
const orderService = new OrderService();

// Order update schema
const UpdateOrderSchema = z.object({
  status: z.enum(['PENDING', 'PAYMENT_PENDING', 'PAYMENT_FAILED', 'CONFIRMED', 'PROCESSING', 'SHIPPED', 'DELIVERED', 'CANCELLED', 'REFUNDED']).optional(),
  paymentStatus: z.enum(['PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED', 'REFUNDED', 'PARTIALLY_REFUNDED']).optional(),
  trackingNumber: z.string().optional(),
  estimatedDelivery: z.string().datetime().optional(),
  actualDelivery: z.string().datetime().optional(),
  adminNotes: z.string().optional(),
});

/**
 * GET /api/orders/[id] - Get order details
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          error: 'Authentication required',
        },
        { status: 401 }
      );
    }

    // Check if user is admin
    const adminEmails = process.env.ADMIN_EMAILS?.split(',') || [];
    const isAdmin = adminEmails.includes(session.user.email || '');

    // Get order details
    const order = await orderService.getOrderDetails(
      params.id,
      isAdmin ? undefined : session.user.id // Admin can see any order
    );

    if (!order) {
      return NextResponse.json(
        {
          success: false,
          error: 'Order not found',
        },
        { status: 404 }
      );
    }

    // Calculate order progress
    const progress = calculateOrderProgress(order.status, order.paymentStatus, order.shipping?.status);

    return NextResponse.json({
      success: true,
      data: {
        ...order,
        progress,
        timeline: generateOrderTimeline(order),
        canCancel: canCancelOrder(order.status, order.paymentStatus),
        canRefund: canRefundOrder(order.status, order.paymentStatus),
      },
    });
  } catch (error) {
    console.error('Get order error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to retrieve order',
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/orders/[id] - Update order (admin only)
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          error: 'Authentication required',
        },
        { status: 401 }
      );
    }

    // Check if user is admin
    const adminEmails = process.env.ADMIN_EMAILS?.split(',') || [];
    if (!adminEmails.includes(session.user.email || '')) {
      return NextResponse.json(
        {
          success: false,
          error: 'Admin access required',
        },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = UpdateOrderSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid request data',
          details: validationResult.error.issues.map((err) => ({
            field: err.path.join('.'),
            message: err.message,
          })),
        },
        { status: 400 }
      );
    }

    const updates = validationResult.data;

    // Convert date strings to Date objects
    if (updates.estimatedDelivery) {
      updates.estimatedDelivery = new Date(updates.estimatedDelivery) as any;
    }
    if (updates.actualDelivery) {
      updates.actualDelivery = new Date(updates.actualDelivery) as any;
    }

    // Update order
    const result = await orderService.updateOrderStatus(
      params.id,
      updates,
      session.user.id
    );

    if (result.success) {
      // Get updated order details
      const updatedOrder = await orderService.getOrderDetails(params.id);

      return NextResponse.json({
        success: true,
        data: updatedOrder,
        message: 'Order updated successfully',
      });
    } else {
      return NextResponse.json(
        {
          success: false,
          error: result.error || 'Failed to update order',
        },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Update order error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update order',
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/orders/[id] - Cancel order
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          error: 'Authentication required',
        },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const reason = searchParams.get('reason') || 'Customer requested cancellation';

    // Check if user owns the order or is admin
    const adminEmails = process.env.ADMIN_EMAILS?.split(',') || [];
    const isAdmin = adminEmails.includes(session.user.email || '');

    const order = await prisma.order.findFirst({
      where: {
        id: params.id,
        ...(isAdmin ? {} : { userId: session.user.id }),
      },
    });

    if (!order) {
      return NextResponse.json(
        {
          success: false,
          error: 'Order not found or access denied',
        },
        { status: 404 }
      );
    }

    // Cancel the order
    const result = await orderService.cancelOrder(
      params.id,
      reason,
      session.user.id
    );

    if (result.success) {
      return NextResponse.json({
        success: true,
        message: 'Order cancelled successfully',
      });
    } else {
      return NextResponse.json(
        {
          success: false,
          error: result.error || 'Failed to cancel order',
        },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Cancel order error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to cancel order',
      },
      { status: 500 }
    );
  }
}

// Helper functions
function calculateOrderProgress(orderStatus: string, paymentStatus: string, shippingStatus?: string): number {
  const statusWeights: Record<string, number> = {
    PENDING: 10,
    PAYMENT_PENDING: 20,
    PAYMENT_FAILED: 0,
    CONFIRMED: 40,
    PROCESSING: 60,
    SHIPPED: 80,
    DELIVERED: 100,
    CANCELLED: 0,
    REFUNDED: 0,
  };

  return statusWeights[orderStatus] || 0;
}

function generateOrderTimeline(order: any): Array<{ status: string; timestamp: Date; description: string; isActive: boolean }> {
  const timeline = [
    {
      status: 'Order Placed',
      timestamp: order.createdAt,
      description: 'Your order has been received and is being processed',
      isActive: true,
    },
  ];

  if (order.paymentStatus === 'COMPLETED') {
    timeline.push({
      status: 'Payment Confirmed',
      timestamp: order.payments?.[0]?.createdAt || order.createdAt,
      description: 'Payment has been successfully processed',
      isActive: true,
    });
  }

  if (order.status === 'CONFIRMED') {
    timeline.push({
      status: 'Order Confirmed',
      timestamp: order.updatedAt,
      description: 'Your order has been confirmed and is being prepared',
      isActive: true,
    });
  }

  if (order.status === 'PROCESSING') {
    timeline.push({
      status: 'In Production',
      timestamp: order.updatedAt,
      description: 'Your custom design is being created',
      isActive: true,
    });
  }

  if (order.status === 'SHIPPED') {
    timeline.push({
      status: 'Shipped',
      timestamp: order.updatedAt,
      description: `Your order is on its way${order.trackingNumber ? ` (Tracking: ${order.trackingNumber})` : ''}`,
      isActive: true,
    });
  }

  if (order.status === 'DELIVERED') {
    timeline.push({
      status: 'Delivered',
      timestamp: order.actualDelivery || order.updatedAt,
      description: 'Your order has been delivered successfully',
      isActive: true,
    });
  }

  // Add estimated delivery if not yet delivered
  if (order.status !== 'DELIVERED' && order.estimatedDelivery) {
    timeline.push({
      status: 'Estimated Delivery',
      timestamp: order.estimatedDelivery,
      description: 'Expected delivery date',
      isActive: false,
    });
  }

  return timeline.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
}

function canCancelOrder(orderStatus: string, paymentStatus: string): boolean {
  return ['PENDING', 'PAYMENT_PENDING', 'CONFIRMED'].includes(orderStatus) &&
         !['COMPLETED'].includes(paymentStatus);
}

function canRefundOrder(orderStatus: string, paymentStatus: string): boolean {
  return paymentStatus === 'COMPLETED' && 
         ['CONFIRMED', 'PROCESSING', 'SHIPPED', 'DELIVERED'].includes(orderStatus);
}
