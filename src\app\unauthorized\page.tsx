'use client';

import { motion } from 'framer-motion';
import { Container, Section } from '@/components/layout';
import { Button } from '@/components/ui';
import { Logo } from '@/components/brand/Logo';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

export default function UnauthorizedPage() {
  const router = useRouter();

  return (
    <div className="min-h-screen bg-gradient-to-br from-warm-50 via-primary-50 to-accent-50 flex items-center justify-center p-4">
      <Section variant="primary" padding="none" className="w-full max-w-md">
        <Container>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="card-premium p-8 text-center space-y-8"
          >
            {/* Logo */}
            <Logo size="lg" showTagline={false} />

            {/* Unauthorized Icon */}
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center"
            >
              <svg
                className="w-8 h-8 text-red-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                />
              </svg>
            </motion.div>

            {/* Content */}
            <div className="space-y-4">
              <h1 className="text-2xl font-bold text-gray-900">
                Access Restricted
              </h1>
              
              <p className="text-gray-600 leading-relaxed">
                This area of the style club is reserved for administrators only. 
                You don't have the necessary permissions to access this content.
              </p>
              
              <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
                <p className="text-blue-800 text-sm leading-relaxed">
                  💡 If you believe you should have access to this area, please contact 
                  our support team for assistance.
                </p>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="space-y-4">
              <Button
                onClick={() => router.back()}
                size="lg"
                fullWidth
              >
                Go Back
              </Button>
              
              <Link href="/">
                <Button variant="outline" size="lg" fullWidth>
                  Return to Homepage
                </Button>
              </Link>
            </div>

            {/* Support Information */}
            <div className="pt-6 border-t border-gray-200">
              <p className="text-sm text-gray-500 mb-3">
                Need help?
              </p>
              <div className="flex flex-col sm:flex-row gap-2 justify-center text-sm">
                <a
                  href="mailto:<EMAIL>"
                  className="text-primary-600 hover:text-primary-700 underline"
                >
                  Contact Support
                </a>
                <span className="hidden sm:inline text-gray-400">•</span>
                <Link
                  href="/help"
                  className="text-primary-600 hover:text-primary-700 underline"
                >
                  Help Center
                </Link>
              </div>
            </div>
          </motion.div>
        </Container>
      </Section>
    </div>
  );
}
