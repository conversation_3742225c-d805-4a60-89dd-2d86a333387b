'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { CheckoutAddress, ShippingMethod } from '@/types/payment';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { EmotionalMessage } from '@/components/ui/EmotionalMessage';

interface ShippingFormProps {
  initialData?: any;
  onSubmit: (data: any) => void;
  onBack?: () => void;
  isLoading: boolean;
  error: string | null;
}

const bangladeshDistricts = [
  'Dhaka', 'Chittagong', 'Sylhet', 'Rajshahi', 'Khulna', 'Barisal', 'Rangpur', 'Mymensingh',
  'Comilla', 'Narayanganj', 'Gazipur', 'Tangail', 'Jamalpur', 'Sherpur', 'Netrokona',
  'Kishoreganj', 'Manikganj', 'Munshiganj', 'Narsingdi', 'Faridpur', 'Gopalganj',
  'Madaripur', 'Rajbari', 'Shariatpur', 'Brahmanbaria', 'Chandpur', 'Lakshmi<PERSON>',
  'Noakhali', '<PERSON><PERSON>', '<PERSON>\'s Bazar', '<PERSON><PERSON><PERSON>', 'Rangamati', 'Khagrach<PERSON>',
];

export function ShippingForm({ initialData, onSubmit, onBack, isLoading, error }: ShippingFormProps) {
  const [address, setAddress] = useState<CheckoutAddress>({
    name: '',
    phone: '',
    email: '',
    street: '',
    area: '',
    city: '',
    district: '',
    postalCode: '',
    country: 'Bangladesh',
    ...initialData?.address,
  });

  const [shippingMethod, setShippingMethod] = useState(initialData?.method || 'standard');
  const [giftOptions, setGiftOptions] = useState({
    isGift: initialData?.isGift || false,
    giftMessage: initialData?.giftMessage || '',
    giftRecipientName: initialData?.giftRecipientName || '',
    giftRecipientEmail: initialData?.giftRecipientEmail || '',
  });

  const [shippingMethods, setShippingMethods] = useState<ShippingMethod[]>([]);
  const [shippingCost, setShippingCost] = useState(0);

  useEffect(() => {
    loadShippingMethods();
  }, [address.district]);

  const loadShippingMethods = async () => {
    try {
      const response = await fetch('/api/payments/create');
      const result = await response.json();
      
      if (result.success) {
        const availableMethods = result.data.shippingMethods.filter((method: ShippingMethod) =>
          method.zones.includes(address.district.toLowerCase()) || method.zones.includes('all')
        );
        setShippingMethods(availableMethods);
        
        // Calculate shipping cost
        const selectedMethod = availableMethods.find((m: ShippingMethod) => m.id === shippingMethod);
        setShippingCost(selectedMethod?.cost || 0);
      }
    } catch (error) {
      console.error('Failed to load shipping methods:', error);
    }
  };

  const handleAddressChange = (field: keyof CheckoutAddress, value: string) => {
    setAddress(prev => ({ ...prev, [field]: value }));
  };

  const handleGiftOptionChange = (field: string, value: string | boolean) => {
    setGiftOptions(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const formData = {
      address,
      method: shippingMethod,
      cost: shippingCost,
      ...giftOptions,
    };

    onSubmit(formData);
  };

  const isFormValid = () => {
    return address.name && address.phone && address.street && address.city && address.district;
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-8">
      {/* Delivery Address */}
      <div className="space-y-6">
        <div className="flex items-center space-x-3">
          <div className="bg-primary-100 rounded-full p-2">
            <svg className="w-5 h-5 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900">Delivery Address</h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Full Name *
            </label>
            <input
              type="text"
              value={address.name}
              onChange={(e) => handleAddressChange('name', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors"
              placeholder="Enter your full name"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Phone Number *
            </label>
            <input
              type="tel"
              value={address.phone}
              onChange={(e) => handleAddressChange('phone', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors"
              placeholder="01XXXXXXXXX"
              required
            />
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Email Address
            </label>
            <input
              type="email"
              value={address.email}
              onChange={(e) => handleAddressChange('email', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors"
              placeholder="<EMAIL> (optional)"
            />
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Street Address *
            </label>
            <textarea
              value={address.street}
              onChange={(e) => handleAddressChange('street', e.target.value)}
              rows={3}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors resize-none"
              placeholder="House/Flat number, Road, Area"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Area/Thana
            </label>
            <input
              type="text"
              value={address.area}
              onChange={(e) => handleAddressChange('area', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors"
              placeholder="Area or Thana name"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              City *
            </label>
            <input
              type="text"
              value={address.city}
              onChange={(e) => handleAddressChange('city', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors"
              placeholder="City name"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              District *
            </label>
            <select
              value={address.district}
              onChange={(e) => handleAddressChange('district', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors"
              required
            >
              <option value="">Select District</option>
              {bangladeshDistricts.map(district => (
                <option key={district} value={district}>{district}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Postal Code
            </label>
            <input
              type="text"
              value={address.postalCode}
              onChange={(e) => handleAddressChange('postalCode', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors"
              placeholder="Postal code (optional)"
            />
          </div>
        </div>
      </div>

      {/* Shipping Method */}
      {shippingMethods.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center space-x-3">
            <div className="bg-warm-100 rounded-full p-2">
              <svg className="w-5 h-5 text-warm-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900">Delivery Method</h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {shippingMethods.map((method) => (
              <motion.label
                key={method.id}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className={`relative flex items-center p-4 border-2 rounded-lg cursor-pointer transition-all ${
                  shippingMethod === method.id
                    ? 'border-primary-500 bg-primary-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <input
                  type="radio"
                  name="shippingMethod"
                  value={method.id}
                  checked={shippingMethod === method.id}
                  onChange={(e) => setShippingMethod(e.target.value)}
                  className="sr-only"
                />
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium text-gray-900">{method.name}</h4>
                    <span className="font-semibold text-primary-600">
                      {method.cost === 0 ? 'Free' : `৳${method.cost}`}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">{method.description}</p>
                  <p className="text-sm text-primary-600 mt-1">{method.estimatedDays}</p>
                  {method.emotionalBenefit && (
                    <p className="text-xs text-warm-600 mt-2 italic">{method.emotionalBenefit}</p>
                  )}
                </div>
                {shippingMethod === method.id && (
                  <div className="absolute top-2 right-2">
                    <div className="bg-primary-500 rounded-full p-1">
                      <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                  </div>
                )}
              </motion.label>
            ))}
          </div>
        </div>
      )}

      {/* Gift Options */}
      <div className="space-y-4">
        <div className="flex items-center space-x-3">
          <div className="bg-pink-100 rounded-full p-2">
            <svg className="w-5 h-5 text-pink-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900">Gift Options</h3>
        </div>

        <label className="flex items-center space-x-3 cursor-pointer">
          <input
            type="checkbox"
            checked={giftOptions.isGift}
            onChange={(e) => handleGiftOptionChange('isGift', e.target.checked)}
            className="w-5 h-5 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
          />
          <span className="text-gray-700">This is a gift</span>
        </label>

        {giftOptions.isGift && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="space-y-4 pl-8"
          >
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Gift Message
              </label>
              <textarea
                value={giftOptions.giftMessage}
                onChange={(e) => handleGiftOptionChange('giftMessage', e.target.value)}
                rows={3}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors resize-none"
                placeholder="Write a heartfelt message for your loved one..."
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Recipient Name
                </label>
                <input
                  type="text"
                  value={giftOptions.giftRecipientName}
                  onChange={(e) => handleGiftOptionChange('giftRecipientName', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors"
                  placeholder="Gift recipient's name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Recipient Email
                </label>
                <input
                  type="email"
                  value={giftOptions.giftRecipientEmail}
                  onChange={(e) => handleGiftOptionChange('giftRecipientEmail', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors"
                  placeholder="<EMAIL>"
                />
              </div>
            </div>
          </motion.div>
        )}
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <svg className="w-5 h-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
            <p className="text-red-700">{error}</p>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-4 pt-6">
        {onBack && (
          <button
            type="button"
            onClick={onBack}
            className="flex-1 px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium"
          >
            Back
          </button>
        )}
        
        <button
          type="submit"
          disabled={!isFormValid() || isLoading}
          className="flex-1 bg-gradient-to-r from-primary-600 to-warm-600 text-white px-6 py-3 rounded-lg font-medium hover:from-primary-700 hover:to-warm-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all flex items-center justify-center space-x-2"
        >
          {isLoading ? (
            <>
              <LoadingSpinner size="sm" />
              <span>Processing...</span>
            </>
          ) : (
            <span>Continue to Payment</span>
          )}
        </button>
      </div>
    </form>
  );
}
