import { getServerSession } from 'next-auth';
import { authOptions } from './auth';
import { NextRequest, NextResponse } from 'next/server';

/**
 * Server-side authentication check
 */
export async function getAuthenticatedUser() {
  const session = await getServerSession(authOptions);
  return session?.user || null;
}

/**
 * Check if user is authenticated
 */
export async function isAuthenticated(): Promise<boolean> {
  const user = await getAuthenticatedUser();
  return !!user;
}

/**
 * Check if user is admin
 */
export async function isAdmin(): Promise<boolean> {
  const user = await getAuthenticatedUser();
  return user?.role === 'admin';
}

/**
 * Require authentication for API routes
 */
export async function requireAuth(request: NextRequest) {
  const session = await getServerSession(authOptions);
  
  if (!session?.user) {
    return NextResponse.json(
      { success: false, error: 'Authentication required' },
      { status: 401 }
    );
  }
  
  return session.user;
}

/**
 * Require admin role for API routes
 */
export async function requireAdmin(request: NextRequest) {
  const session = await getServerSession(authOptions);
  
  if (!session?.user) {
    return NextResponse.json(
      { success: false, error: 'Authentication required' },
      { status: 401 }
    );
  }
  
  if (session.user.role !== 'admin') {
    return NextResponse.json(
      { success: false, error: 'Admin access required' },
      { status: 403 }
    );
  }
  
  return session.user;
}

/**
 * Check if user owns resource or is admin
 */
export async function canAccessResource(resourceUserId: string): Promise<boolean> {
  const user = await getAuthenticatedUser();
  
  if (!user) return false;
  if (user.role === 'admin') return true;
  if (user.id === resourceUserId) return true;
  
  return false;
}

/**
 * Get user role
 */
export async function getUserRole(): Promise<'admin' | 'user' | null> {
  const user = await getAuthenticatedUser();
  return user?.role || null;
}

/**
 * Check if user has specific permissions
 */
export async function hasPermission(permission: string): Promise<boolean> {
  const user = await getAuthenticatedUser();
  
  if (!user) return false;
  if (user.role === 'admin') return true;
  
  // Add more granular permissions here if needed
  const userPermissions: Record<string, string[]> = {
    user: ['create_design', 'view_templates', 'use_ai_tryon', 'manage_profile'],
    admin: ['*'], // Admin has all permissions
  };
  
  const rolePermissions = userPermissions[user.role] || [];
  return rolePermissions.includes('*') || rolePermissions.includes(permission);
}

/**
 * Create unauthorized response
 */
export function createUnauthorizedResponse(message = 'Unauthorized') {
  return NextResponse.json(
    { success: false, error: message },
    { status: 401 }
  );
}

/**
 * Create forbidden response
 */
export function createForbiddenResponse(message = 'Forbidden') {
  return NextResponse.json(
    { success: false, error: message },
    { status: 403 }
  );
}
