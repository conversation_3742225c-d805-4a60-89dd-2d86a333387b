# Authentication System - Ottiq Style Club

## Overview

The Ottiq authentication system is designed to make sign-in feel like joining an exclusive style club. It emphasizes emotional connection, lifestyle-driven copy, and a premium brand experience while maintaining robust security and role-based access controls.

## Features Implemented

### 🎨 Branded Authentication Experience
- **Style Club Messaging**: Sign-in feels like joining an exclusive community
- **Emotional Copy**: Lifestyle-driven language that emphasizes self-expression and creativity
- **Premium Design**: Gradient backgrounds, glass morphism effects, and smooth animations
- **Mobile-First**: Optimized for touch interactions and mobile viewports

### 🔐 OAuth Providers
- **Google OAuth**: Primary authentication method
- **Facebook OAuth**: Secondary authentication method
- **Required for AI Try-On**: Both providers needed for AI features

### 👥 Role-Based Access Control
- **User Role**: Standard members with access to creation tools
- **Admin Role**: Full access to admin panel and management features
- **Dynamic Role Assignment**: Based on email configuration in environment variables

### 🛡️ Security Features
- **Server-Side Session Management**: Database-backed sessions via Prisma
- **Middleware Protection**: Route-level authentication and authorization
- **API Route Guards**: Utility functions for protecting API endpoints
- **CSRF Protection**: Built-in NextAuth security features

## File Structure

```
src/
├── app/
│   ├── api/auth/[...nextauth]/route.ts    # NextAuth API handler
│   ├── auth/
│   │   ├── signin/page.tsx                # Branded sign-in page
│   │   └── error/page.tsx                 # Branded error page
│   └── unauthorized/page.tsx              # Unauthorized access page
├── components/
│   ├── auth/
│   │   ├── LoginButton.tsx                # Smart login/user menu button
│   │   ├── UserMenu.tsx                   # User dropdown menu
│   │   ├── AuthGuard.tsx                  # Component-level auth protection
│   │   └── index.ts                       # Auth component exports
│   ├── layout/
│   │   └── Navigation.tsx                 # Main navigation with auth
│   └── providers/
│       └── SessionProvider.tsx            # NextAuth session provider
├── lib/
│   ├── auth.ts                           # NextAuth configuration
│   └── auth-utils.ts                     # Server-side auth utilities
├── middleware.ts                         # Route protection middleware
└── __tests__/auth/                       # Authentication tests
```

## Environment Variables

Add these to your `.env.local` file:

```bash
# NextAuth Configuration
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here"

# OAuth Providers
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
FACEBOOK_CLIENT_ID="your-facebook-client-id"
FACEBOOK_CLIENT_SECRET="your-facebook-client-secret"

# Admin Configuration
ADMIN_EMAILS="<EMAIL>,<EMAIL>"
```

## Usage Examples

### Protecting Pages with AuthGuard

```tsx
import { AuthGuard } from '@/components/auth';

export default function ProtectedPage() {
  return (
    <AuthGuard requireAuth>
      <div>This content requires authentication</div>
    </AuthGuard>
  );
}

// For admin-only pages
export default function AdminPage() {
  return (
    <AuthGuard requireAdmin>
      <div>This content requires admin access</div>
    </AuthGuard>
  );
}
```

### Protecting API Routes

```tsx
import { requireAuth, requireAdmin } from '@/lib/auth-utils';

export async function GET(request: NextRequest) {
  const user = await requireAuth(request);
  if (user instanceof NextResponse) return user; // Error response
  
  // User is authenticated, proceed with logic
  return NextResponse.json({ user });
}

export async function POST(request: NextRequest) {
  const admin = await requireAdmin(request);
  if (admin instanceof NextResponse) return admin; // Error response
  
  // User is admin, proceed with admin logic
  return NextResponse.json({ success: true });
}
```

### Using Auth Components

```tsx
import { LoginButton, UserMenu } from '@/components/auth';

// Smart button that shows login or user menu based on auth state
<LoginButton variant="primary" size="lg" />

// Or use components separately
const { data: session } = useSession();
{session ? <UserMenu /> : <LoginButton />}
```

## Brand Experience Details

### Sign-In Page Features
- **Emotional Headline**: "Join the Style Club"
- **Lifestyle Copy**: Emphasizes creativity, self-expression, and community
- **Visual Benefits**: Shows what users unlock when they join
- **Social Proof**: Premium feel with gradient designs and animations
- **Privacy Transparency**: Clear privacy policy and terms links

### Error Handling
- **Helpful Error Messages**: User-friendly explanations for different error types
- **Recovery Actions**: Clear next steps for users to resolve issues
- **Support Integration**: Easy access to help and support channels
- **Consistent Branding**: Maintains style club aesthetic even in error states

## Testing

The authentication system includes comprehensive tests:

- **Unit Tests**: Auth utilities and component logic
- **Integration Tests**: Component interactions and state management
- **E2E Tests**: Complete authentication flows and user journeys

Run tests with:
```bash
npm test                    # Unit tests
npm run test:e2e           # E2E tests
```

## Security Considerations

1. **Environment Variables**: Never commit OAuth secrets to version control
2. **Admin Emails**: Configure admin emails in environment variables only
3. **Session Security**: Database sessions provide better security than JWT-only
4. **HTTPS Required**: OAuth providers require HTTPS in production
5. **CSRF Protection**: NextAuth provides built-in CSRF protection

## Next Steps

1. **Configure OAuth Apps**: Set up Google and Facebook OAuth applications
2. **Set Environment Variables**: Add all required environment variables
3. **Test Authentication**: Verify sign-in flows work correctly
4. **Customize Admin Logic**: Adjust admin role assignment as needed
5. **Add More Providers**: Consider additional OAuth providers if needed

## Support

For authentication issues:
- Check environment variable configuration
- Verify OAuth app settings match your domain
- Review NextAuth documentation for provider-specific setup
- Test with different browsers and devices
